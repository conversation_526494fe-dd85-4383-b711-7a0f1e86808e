import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { generateApiKey, generateApiSecret, encryptApiSecret } from "@/lib/crypto"
import { z } from "zod"

const publisherOnboardingSchema = z.object({
  websiteUrl: z.string().url("Invalid website URL"),
  websiteName: z.string().min(2, "Website name must be at least 2 characters"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  monthlyTraffic: z.string().min(1, "Monthly traffic is required"),
  primaryRegion: z.string().min(1, "Primary region is required"),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = publisherOnboardingSchema.parse(body)

    // Check if user already has a publisher profile
    const existingProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (existingProfile) {
      return NextResponse.json(
        { message: "Publisher profile already exists" },
        { status: 400 }
      )
    }

    // Generate API credentials
    const apiKey = generateApiKey()
    const apiSecret = generateApiSecret()
    const encryptedSecret = encryptApiSecret(apiSecret)

    // Create publisher profile
    const publisherProfile = await prisma.publisherProfile.create({
      data: {
        userId: session.user.id,
        websiteUrl: validatedData.websiteUrl,
        websiteName: validatedData.websiteName,
        description: validatedData.description,
        category: validatedData.category,
        monthlyTraffic: parseInt(validatedData.monthlyTraffic),
        region: validatedData.primaryRegion,
        apiKey,
        apiSecret: encryptedSecret,
      },
    })

    // Update user onboarding status
    await prisma.user.update({
      where: { id: session.user.id },
      data: { isOnboarded: true },
    })

    return NextResponse.json(
      {
        message: "Publisher profile created successfully",
        profile: {
          id: publisherProfile.id,
          websiteName: publisherProfile.websiteName,
          websiteUrl: publisherProfile.websiteUrl,
          apiKey: publisherProfile.apiKey,
          // Don't return the encrypted secret for security
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Publisher onboarding error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
