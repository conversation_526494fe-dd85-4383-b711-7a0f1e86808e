"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { 
  Key, 
  Copy, 
  Eye, 
  EyeOff, 
  RefreshCw, 
  Shield, 
  Code, 
  AlertTriangle,
  CheckCircle,
  Globe
} from "lucide-react"
import { toast } from "sonner"

interface ApiCredentials {
  apiKey: string
  apiSecret: string
  isActive: boolean
  lastUsed?: Date
  createdAt: Date
}

export default function ApiKeysPage() {
  const [credentials, setCredentials] = useState<ApiCredentials | null>(null)
  const [showSecret, setShowSecret] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isRegenerating, setIsRegenerating] = useState(false)

  useEffect(() => {
    fetchApiCredentials()
  }, [])

  const fetchApiCredentials = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      setCredentials({
        apiKey: "ak_live_1234567890abcdef1234567890abcdef12345678",
        apiSecret: "sk_live_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
        isActive: true,
        lastUsed: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        createdAt: new Date("2024-01-15"),
      })
    } catch (error) {
      console.error("Failed to fetch API credentials:", error)
      toast.error("Failed to load API credentials")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard!`)
  }

  const regenerateApiSecret = async () => {
    try {
      setIsRegenerating(true)
      // Mock regeneration
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      if (credentials) {
        setCredentials({
          ...credentials,
          apiSecret: "sk_live_" + Math.random().toString(36).substring(2, 50),
        })
      }
      
      toast.success("API secret regenerated successfully!")
    } catch (error) {
      console.error("Failed to regenerate API secret:", error)
      toast.error("Failed to regenerate API secret")
    } finally {
      setIsRegenerating(false)
    }
  }

  const integrationCode = `<!-- Add this to your website's <head> section -->
<script src="https://ad-network-new-web-app.vercel.app/sdk.js"></script>

<!-- Add this where you want to display ads -->
<div id="ad-container"></div>

<script>
  // Initialize the Ad Network SDK
  AdNetwork.init({
    apiKey: '${credentials?.apiKey || 'YOUR_API_KEY'}',
    container: 'ad-container',
    // Optional configuration
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
    onAdLoaded: function(ad) {
      console.log('Ad loaded:', ad);
    },
    onAdClicked: function(ad) {
      console.log('Ad clicked:', ad);
    }
  });
</script>`

  const reactIntegrationCode = `import { useEffect } from 'react';

function AdComponent() {
  useEffect(() => {
    // Load the Ad Network SDK
    const script = document.createElement('script');
    script.src = 'https://ad-network-new-web-app.vercel.app/sdk.js';
    script.onload = () => {
      window.AdNetwork.init({
        apiKey: '${credentials?.apiKey || 'YOUR_API_KEY'}',
        container: 'react-ad-container',
        autoRefresh: true,
      });
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup
      document.head.removeChild(script);
    };
  }, []);

  return (
    <div 
      id="react-ad-container" 
      style={{ width: '728px', height: '90px' }}
    />
  );
}

export default AdComponent;`

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading API credentials...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
        {/* Header */}
        <div>
          <h2 className="text-3xl font-bold tracking-tight">API Keys</h2>
          <p className="text-muted-foreground">
            Manage your API credentials and integrate ads into your website
          </p>
        </div>

        {/* Security Alert */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Keep your API credentials secure. Never expose your API secret in client-side code or public repositories.
          </AlertDescription>
        </Alert>

        {/* API Credentials */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              API Credentials
            </CardTitle>
            <CardDescription>
              Use these credentials to integrate ads into your website
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {credentials && (
              <>
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key">API Key</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="api-key"
                        value={credentials.apiKey}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(credentials.apiKey, "API Key")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Use this in your client-side integration
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="api-secret">API Secret</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="api-secret"
                        type={showSecret ? "text" : "password"}
                        value={credentials.apiSecret}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setShowSecret(!showSecret)}
                      >
                        {showSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(credentials.apiSecret, "API Secret")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Keep this secret! Only use in server-side code
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant={credentials.isActive ? "default" : "secondary"}>
                        {credentials.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {credentials.isActive && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Created: {credentials.createdAt.toLocaleDateString()}
                      {credentials.lastUsed && (
                        <> • Last used: {credentials.lastUsed.toLocaleString()}</>
                      )}
                    </p>
                  </div>
                  
                  <Button
                    variant="outline"
                    onClick={regenerateApiSecret}
                    disabled={isRegenerating}
                  >
                    {isRegenerating ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    Regenerate Secret
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Integration Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Integration Guide
            </CardTitle>
            <CardDescription>
              Learn how to integrate ads into your website
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="html" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="html">HTML/JavaScript</TabsTrigger>
                <TabsTrigger value="react">React</TabsTrigger>
                <TabsTrigger value="wordpress">WordPress</TabsTrigger>
              </TabsList>
              
              <TabsContent value="html" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Basic Integration</h4>
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{integrationCode}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(integrationCode, "Integration code")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="react" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">React Component</h4>
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{reactIntegrationCode}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(reactIntegrationCode, "React code")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="wordpress" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">WordPress Integration</h4>
                  <div className="space-y-4">
                    <Alert>
                      <Globe className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Coming Soon:</strong> We're working on a WordPress plugin to make integration even easier.
                      </AlertDescription>
                    </Alert>
                    <p className="text-sm text-muted-foreground">
                      For now, you can add the HTML/JavaScript code to your WordPress theme files or use a code injection plugin.
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle>Best Practices</CardTitle>
            <CardDescription>
              Tips for optimal ad performance and security
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Security</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Never expose your API secret in client-side code</li>
                  <li>• Regenerate credentials if compromised</li>
                  <li>• Use HTTPS for all API requests</li>
                  <li>• Monitor API usage regularly</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Performance</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Place ads in high-visibility areas</li>
                  <li>• Use appropriate ad sizes for your layout</li>
                  <li>• Enable auto-refresh for better revenue</li>
                  <li>• Monitor CTR and adjust placement</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
