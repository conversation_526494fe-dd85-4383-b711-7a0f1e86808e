"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import {
  Key,
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  Shield,
  Code,
  AlertTriangle,
  CheckCircle,
  Globe,
  Layers
} from "lucide-react"
import { toast } from "sonner"

interface ApiCredentials {
  apiKey: string
  apiSecret: string
  isActive: boolean
  lastUsed?: Date
  createdAt: Date
}

interface AdSpace {
  id: string
  name: string
  format: string
  width: number
  height: number
  position: string
  isActive: boolean
}

export default function ApiKeysPage() {
  const [credentials, setCredentials] = useState<ApiCredentials | null>(null)
  const [adSpaces, setAdSpaces] = useState<AdSpace[]>([])
  const [selectedAdSpace, setSelectedAdSpace] = useState<string>("")
  const [showSecret, setShowSecret] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isRegenerating, setIsRegenerating] = useState(false)

  useEffect(() => {
    fetchApiCredentials()
    fetchAdSpaces()
  }, [])

  const fetchApiCredentials = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/publisher/api-keys")
      if (response.ok) {
        const data = await response.json()
        setCredentials({
          apiKey: data.credentials.apiKey,
          apiSecret: data.credentials.apiSecret,
          isActive: true,
          lastUsed: data.usage.lastUsed ? new Date(data.usage.lastUsed) : undefined,
          createdAt: new Date(data.credentials.createdAt),
        })
      } else {
        // Fallback to mock data if API fails
        setCredentials({
          apiKey: "ak_live_1234567890abcdef1234567890abcdef12345678",
          apiSecret: "sk_live_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
          isActive: true,
          lastUsed: new Date(Date.now() - 1000 * 60 * 30),
          createdAt: new Date("2024-01-15"),
        })
      }
    } catch (error) {
      console.error("Failed to fetch API credentials:", error)
      toast.error("Failed to load API credentials")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchAdSpaces = async () => {
    try {
      const response = await fetch("/api/publisher/ad-spaces")
      if (response.ok) {
        const data = await response.json()
        setAdSpaces(data.adSpaces || [])
        // Auto-select first active ad space
        const activeAdSpace = data.adSpaces?.find((space: AdSpace) => space.isActive)
        if (activeAdSpace) {
          setSelectedAdSpace(activeAdSpace.id)
        }
      }
    } catch (error) {
      console.error("Failed to fetch ad spaces:", error)
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard!`)
  }

  const regenerateApiSecret = async () => {
    try {
      setIsRegenerating(true)
      // Mock regeneration
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      if (credentials) {
        setCredentials({
          ...credentials,
          apiSecret: "sk_live_" + Math.random().toString(36).substring(2, 50),
        })
      }
      
      toast.success("API secret regenerated successfully!")
    } catch (error) {
      console.error("Failed to regenerate API secret:", error)
      toast.error("Failed to regenerate API secret")
    } finally {
      setIsRegenerating(false)
    }
  }

  const selectedAdSpaceData = adSpaces.find(space => space.id === selectedAdSpace)
  const hasAdSpaces = adSpaces.length > 0
  const apiKey = credentials?.apiKey || 'YOUR_API_KEY'
  const adSpaceId = selectedAdSpace || 'YOUR_AD_SPACE_ID'
  const adWidth = selectedAdSpaceData?.width || 728
  const adHeight = selectedAdSpaceData?.height || 90
  const adFormat = selectedAdSpaceData?.format || 'BANNER'

  const integrationCode = hasAdSpaces ? `<!-- Add this to your website's <head> section -->
<script src="https://ad-network-new-web-app.vercel.app/sdk.js"></script>

<!-- Add this where you want to display ads -->
<div id="ad-container" style="width: ${adWidth}px; height: ${adHeight}px;"></div>

<script>
  // Initialize the Ad Network SDK
  AdNetwork.init({
    apiKey: '${apiKey}',
    debug: true // Remove in production
  });

  // Load ad with specific ad space
  AdNetwork.loadAd('ad-container', {
    adSpaceId: '${adSpaceId}',
    format: '${adFormat}',
    width: ${adWidth},
    height: ${adHeight},
    position: '${selectedAdSpaceData?.position || 'header'}'
  });
</script>` : `<!-- You need to create ad spaces first! -->
<!-- Go to Ad Spaces page to create your first ad space -->
<div style="padding: 20px; border: 2px dashed #ccc; text-align: center;">
  <p>⚠️ No ad spaces found. Please create an ad space first.</p>
  <p>Go to Dashboard → Ad Spaces to create your first ad space.</p>
</div>`

  const reactIntegrationCode = hasAdSpaces ? `import { useEffect } from 'react';

function AdComponent() {
  useEffect(() => {
    // Load the Ad Network SDK
    const script = document.createElement('script');
    script.src = 'https://ad-network-new-web-app.vercel.app/sdk.js';
    script.onload = () => {
      // Initialize SDK
      window.AdNetwork.init({
        apiKey: '${apiKey}',
        debug: true // Remove in production
      });

      // Load ad with specific ad space
      window.AdNetwork.loadAd('react-ad-container', {
        adSpaceId: '${adSpaceId}',
        format: '${adFormat}',
        width: ${adWidth},
        height: ${adHeight},
        position: '${selectedAdSpaceData?.position || 'header'}'
      });
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  return (
    <div
      id="react-ad-container"
      style={{
        width: '${adWidth}px',
        height: '${adHeight}px',
        border: '1px solid #ddd',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9'
      }}
    >
      Loading ad...
    </div>
  );
}

export default AdComponent;` : `// You need to create ad spaces first!
// Go to Dashboard → Ad Spaces to create your first ad space

import React from 'react';

function AdComponent() {
  return (
    <div style={{
      padding: '20px',
      border: '2px dashed #ccc',
      textAlign: 'center',
      backgroundColor: '#f9f9f9'
    }}>
      <p>⚠️ No ad spaces found. Please create an ad space first.</p>
      <p>Go to Dashboard → Ad Spaces to create your first ad space.</p>
    </div>
  );
}

export default AdComponent;`

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading API credentials...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
        {/* Header */}
        <div>
          <h2 className="text-3xl font-bold tracking-tight">API Keys</h2>
          <p className="text-muted-foreground">
            Manage your API credentials and integrate ads into your website
          </p>
        </div>

        {/* Security Alert */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Keep your API credentials secure. Never expose your API secret in client-side code or public repositories.
          </AlertDescription>
        </Alert>

        {/* API Credentials */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              API Credentials
            </CardTitle>
            <CardDescription>
              Use these credentials to integrate ads into your website
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {credentials && (
              <>
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key">API Key</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="api-key"
                        value={credentials.apiKey}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(credentials.apiKey, "API Key")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Use this in your client-side integration
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="api-secret">API Secret</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="api-secret"
                        type={showSecret ? "text" : "password"}
                        value={credentials.apiSecret}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setShowSecret(!showSecret)}
                      >
                        {showSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(credentials.apiSecret, "API Secret")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Keep this secret! Only use in server-side code
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant={credentials.isActive ? "default" : "secondary"}>
                        {credentials.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {credentials.isActive && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Created: {credentials.createdAt.toLocaleDateString()}
                      {credentials.lastUsed && (
                        <> • Last used: {credentials.lastUsed.toLocaleString()}</>
                      )}
                    </p>
                  </div>
                  
                  <Button
                    variant="outline"
                    onClick={regenerateApiSecret}
                    disabled={isRegenerating}
                  >
                    {isRegenerating ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    Regenerate Secret
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Integration Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Integration Guide
            </CardTitle>
            <CardDescription>
              Copy and paste the code below to integrate ads into your website
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Ad Space Selector */}
            {hasAdSpaces && (
              <div className="mb-6 p-4 bg-muted rounded-lg">
                <Label htmlFor="ad-space-select" className="text-sm font-medium mb-2 block">
                  Select Ad Space for Integration Code:
                </Label>
                <Select value={selectedAdSpace} onValueChange={setSelectedAdSpace}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Choose an ad space..." />
                  </SelectTrigger>
                  <SelectContent>
                    {adSpaces.map((space) => (
                      <SelectItem key={space.id} value={space.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {space.format}
                          </Badge>
                          <span>{space.name}</span>
                          <span className="text-muted-foreground text-xs">
                            ({space.width}×{space.height})
                          </span>
                          {!space.isActive && (
                            <Badge variant="secondary" className="text-xs">
                              Inactive
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedAdSpaceData && (
                  <div className="mt-2 text-sm text-muted-foreground">
                    Selected: <strong>{selectedAdSpaceData.name}</strong> •
                    {selectedAdSpaceData.format} •
                    {selectedAdSpaceData.width}×{selectedAdSpaceData.height} •
                    Position: {selectedAdSpaceData.position}
                    {!selectedAdSpaceData.isActive && (
                      <span className="text-orange-600 ml-2">⚠️ This ad space is inactive</span>
                    )}
                  </div>
                )}
              </div>
            )}

            {!hasAdSpaces && (
              <Alert className="mb-6">
                <Layers className="h-4 w-4" />
                <AlertDescription>
                  <strong>No ad spaces found!</strong> You need to create at least one ad space before you can integrate ads.
                  <br />
                  <Button variant="link" className="p-0 h-auto" onClick={() => window.location.href = '/dashboard/publisher/ad-spaces'}>
                    Go to Ad Spaces →
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="html" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="html">HTML/JavaScript</TabsTrigger>
                <TabsTrigger value="react">React</TabsTrigger>
                <TabsTrigger value="wordpress">WordPress</TabsTrigger>
              </TabsList>
              
              <TabsContent value="html" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    HTML/JavaScript Integration
                    {hasAdSpaces && selectedAdSpaceData && (
                      <Badge variant="outline" className="text-xs">
                        Ready to use!
                      </Badge>
                    )}
                  </h4>
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto max-h-96">
                      <code>{integrationCode}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(integrationCode, "Integration code")}
                      disabled={!hasAdSpaces}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  {hasAdSpaces && selectedAdSpaceData && (
                    <div className="text-sm text-muted-foreground mt-2">
                      💡 This code is customized for your "<strong>{selectedAdSpaceData.name}</strong>" ad space.
                      Just copy and paste it into your website!
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="react" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    React Component
                    {hasAdSpaces && selectedAdSpaceData && (
                      <Badge variant="outline" className="text-xs">
                        Ready to use!
                      </Badge>
                    )}
                  </h4>
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto max-h-96">
                      <code>{reactIntegrationCode}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(reactIntegrationCode, "React code")}
                      disabled={!hasAdSpaces}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  {hasAdSpaces && selectedAdSpaceData && (
                    <div className="text-sm text-muted-foreground mt-2">
                      💡 This React component is customized for your "<strong>{selectedAdSpaceData.name}</strong>" ad space.
                      Import and use it in your React app!
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="wordpress" className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">WordPress Integration</h4>
                  <div className="space-y-4">
                    <Alert>
                      <Globe className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Coming Soon:</strong> We're working on a WordPress plugin to make integration even easier.
                      </AlertDescription>
                    </Alert>
                    <p className="text-sm text-muted-foreground">
                      For now, you can add the HTML/JavaScript code to your WordPress theme files or use a code injection plugin like "Insert Headers and Footers".
                    </p>
                    {hasAdSpaces && (
                      <div className="bg-muted p-4 rounded-lg">
                        <h5 className="font-medium mb-2">WordPress Instructions:</h5>
                        <ol className="text-sm space-y-1 list-decimal list-inside">
                          <li>Install a code injection plugin (e.g., "Insert Headers and Footers")</li>
                          <li>Copy the HTML/JavaScript code from the first tab</li>
                          <li>Paste it in the plugin's "Body" or "Footer" section</li>
                          <li>Save and check your website</li>
                        </ol>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Troubleshooting Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Troubleshooting
            </CardTitle>
            <CardDescription>
              Common issues and solutions for ad integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">🚫 Ads not showing?</h4>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li><strong>Check ad space status:</strong> Make sure your ad space is active</li>
                  <li><strong>Verify API key:</strong> Ensure you're using the correct API key</li>
                  <li><strong>Check ad space ID:</strong> Make sure the adSpaceId in your code matches your ad space</li>
                  <li><strong>Active campaigns:</strong> Ensure there are active campaigns targeting your category/region</li>
                  <li><strong>Browser console:</strong> Check for JavaScript errors in browser developer tools</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">🔧 Debug Mode</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  The integration code includes <code className="bg-muted px-1 rounded">debug: true</code> which will show helpful messages in your browser console.
                </p>
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Pro tip:</strong> Open your browser's developer tools (F12) and check the Console tab for debug messages when testing your integration.
                  </AlertDescription>
                </Alert>
              </div>

              <div>
                <h4 className="font-medium mb-2">📊 Testing Your Integration</h4>
                <div className="grid gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">1</Badge>
                    <span>Add the code to your website</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">2</Badge>
                    <span>Open browser developer tools (F12)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">3</Badge>
                    <span>Look for "AdNetwork SDK" messages in Console</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">4</Badge>
                    <span>Check if ads load within 5-10 seconds</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle>Best Practices</CardTitle>
            <CardDescription>
              Tips for optimal ad performance and security
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Security</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Never expose your API secret in client-side code</li>
                  <li>• Regenerate credentials if compromised</li>
                  <li>• Use HTTPS for all API requests</li>
                  <li>• Monitor API usage regularly</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Performance</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Place ads in high-visibility areas</li>
                  <li>• Use appropriate ad sizes for your layout</li>
                  <li>• Enable auto-refresh for better revenue</li>
                  <li>• Monitor CTR and adjust placement</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
