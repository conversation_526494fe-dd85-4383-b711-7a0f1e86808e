import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const statusUpdateSchema = z.object({
  status: z.enum(["DRAFT", "PENDING", "ACTIVE", "PAUSED", "COMPLETED", "CANCELLED"])
})

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = statusUpdateSchema.parse(body)
    const { status } = validatedData

    // Get the campaign and verify ownership
    const campaign = await prisma.campaign.findUnique({
      where: { id },
      include: {
        advertiser: {
          include: {
            user: true
          }
        },
        ads: true
      }
    })

    if (!campaign) {
      return NextResponse.json(
        { error: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Validate status transition
    const validTransitions = getValidStatusTransitions(campaign.status)
    if (!validTransitions.includes(status)) {
      return NextResponse.json(
        { error: `Cannot transition from ${campaign.status} to ${status}` },
        { status: 400 }
      )
    }

    // Additional validation for ACTIVE status
    if (status === "ACTIVE") {
      const validationResult = await validateCampaignForActivation(campaign)
      if (!validationResult.valid) {
        return NextResponse.json(
          { error: validationResult.error },
          { status: 400 }
        )
      }
    }

    // Update campaign status
    const updatedCampaign = await prisma.campaign.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date(),
        // Set start date when activating
        ...(status === "ACTIVE" && !campaign.startDate && {
          startDate: new Date()
        })
      },
      include: {
        advertiser: {
          select: {
            companyName: true,
            balance: true
          }
        },
        ads: {
          select: {
            id: true,
            title: true,
            isActive: true
          }
        },
        _count: {
          select: {
            adPlacements: true
          }
        }
      }
    })

    // Log status change for analytics
    console.log(`Campaign ${id} status changed from ${campaign.status} to ${status}`)

    // In a real implementation, you might:
    // 1. Send notifications to relevant parties
    // 2. Update real-time dashboards
    // 3. Trigger campaign optimization algorithms
    // 4. Update budget allocation systems
    // 5. Send webhooks to external systems

    return NextResponse.json({
      success: true,
      campaign: updatedCampaign,
      message: `Campaign ${status.toLowerCase()} successfully`
    })

  } catch (error) {
    console.error("Campaign status update error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

function getValidStatusTransitions(currentStatus: string): string[] {
  const transitions: Record<string, string[]> = {
    DRAFT: ["PENDING", "CANCELLED"],
    PENDING: ["ACTIVE", "CANCELLED"],
    ACTIVE: ["PAUSED", "COMPLETED", "CANCELLED"],
    PAUSED: ["ACTIVE", "CANCELLED"],
    COMPLETED: [], // Terminal state
    CANCELLED: [] // Terminal state
  }

  return transitions[currentStatus] || []
}

async function validateCampaignForActivation(campaign: any): Promise<{ valid: boolean; error?: string }> {
  // Check if campaign has ads
  if (!campaign.ads || campaign.ads.length === 0) {
    return { valid: false, error: "Campaign must have at least one ad" }
  }

  // Check if campaign has active ads
  const activeAds = campaign.ads.filter((ad: any) => ad.isActive)
  if (activeAds.length === 0) {
    return { valid: false, error: "Campaign must have at least one active ad" }
  }

  // Check advertiser balance
  if (campaign.advertiser.balance <= 0) {
    return { valid: false, error: "Insufficient advertiser balance" }
  }

  // Check if budget is sufficient
  if (campaign.budget <= 0) {
    return { valid: false, error: "Campaign budget must be greater than 0" }
  }

  // Check if bid amount is reasonable
  if (campaign.bidAmount <= 0) {
    return { valid: false, error: "Bid amount must be greater than 0" }
  }

  // Check targeting settings
  if (!campaign.targetRegions || campaign.targetRegions.length === 0) {
    return { valid: false, error: "Campaign must have target regions" }
  }

  if (!campaign.targetCategories || campaign.targetCategories.length === 0) {
    return { valid: false, error: "Campaign must have target categories" }
  }

  // Check if end date is in the future (if set)
  if (campaign.endDate && new Date(campaign.endDate) <= new Date()) {
    return { valid: false, error: "Campaign end date must be in the future" }
  }

  return { valid: true }
}

// GET endpoint to retrieve campaign status and metrics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const campaign = await prisma.campaign.findUnique({
      where: { id },
      include: {
        advertiser: {
          include: {
            user: true
          }
        },
        ads: {
          select: {
            id: true,
            title: true,
            isActive: true,
            format: true,
            width: true,
            height: true
          }
        },
        adPlacements: {
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            cost: true,
            revenue: true,
            createdAt: true
          }
        }
      }
    })

    if (!campaign) {
      return NextResponse.json(
        { error: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Calculate metrics
    const totalImpressions = campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
    const totalClicks = campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
    const totalConversions = campaign.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
    const totalCost = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0

    return NextResponse.json({
      campaign: {
        ...campaign,
        metrics: {
          totalImpressions,
          totalClicks,
          totalConversions,
          totalCost,
          ctr: Number(ctr.toFixed(2)),
          conversionRate: Number(conversionRate.toFixed(2)),
          remainingBudget: campaign.budget - totalCost
        }
      }
    })

  } catch (error) {
    console.error("Campaign status retrieval error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
