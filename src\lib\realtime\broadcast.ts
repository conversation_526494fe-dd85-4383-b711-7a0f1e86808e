// Store active connections
const connections = new Map<string, ReadableStreamDefaultController>()

// Broadcast update to all connected clients
export function broadcastUpdate(update: any) {
  const message = JSON.stringify({
    type: "broadcast_update",
    data: update,
    timestamp: new Date().toISOString()
  })

  connections.forEach((controller, connectionId) => {
    try {
      controller.enqueue(`data: ${message}\n\n`)
    } catch (error) {
      console.error(`Failed to send update to connection ${connectionId}:`, error)
      // Remove dead connection
      connections.delete(connectionId)
    }
  })
}

export function addConnection(connectionId: string, controller: ReadableStreamDefaultController) {
  connections.set(connectionId, controller)
}

export function removeConnection(connectionId: string) {
  connections.delete(connectionId)
}