"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import { 
  ArrowLeft, 
  ArrowRight, 
  CalendarIcon, 
  Target, 
  DollarSign, 
  Settings,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { PricingModel, AdFormat, CampaignCreateData } from "@/lib/types"

const steps = [
  { id: 1, title: "Basic Info", icon: Settings },
  { id: 2, title: "Budget & Pricing", icon: DollarSign },
  { id: 3, title: "Targeting", icon: Target },
  { id: 4, title: "Schedule", icon: CalendarIcon },
  { id: 5, title: "Review", icon: CheckCircle },
]

const regions = [
  "North America",
  "Europe", 
  "Asia Pacific",
  "Latin America",
  "Middle East",
  "Africa",
  "Global"
]

const categories = [
  "Technology",
  "Business",
  "Entertainment",
  "Sports",
  "News",
  "Lifestyle",
  "Health",
  "Education",
  "Travel",
  "Food",
  "Fashion",
  "Gaming",
  "Other"
]

export default function CreateCampaignPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [campaignData, setCampaignData] = useState<CampaignCreateData & { adFormat: AdFormat; frequencyCap: number; frequencyPeriod: string }>({
    name: "",
    description: "",
    budget: 1000,
    dailyBudget: 50,
    pricingModel: "CPC",
    bidAmount: 0.50,
    targetRegions: [],
    targetCategories: [],
    startDate: new Date(),
    adFormat: "BANNER",
    frequencyCap: 5,
    frequencyPeriod: "DAILY",
  })

  const handleInputChange = (field: keyof (CampaignCreateData & { adFormat: AdFormat; frequencyCap: number; frequencyPeriod: string }), value: any) => {
    setCampaignData(prev => ({ ...prev, [field]: value }))
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        if (!campaignData.name.trim()) {
          toast.error("Campaign name is required")
          return false
        }
        return true
      case 2:
        if (campaignData.budget <= 0 || (campaignData.dailyBudget || 0) <= 0 || campaignData.bidAmount <= 0) {
          toast.error("All budget fields must be greater than 0")
          return false
        }
        if ((campaignData.dailyBudget || 0) > campaignData.budget) {
          toast.error("Daily budget cannot exceed total budget")
          return false
        }
        return true
      case 3:
        if (campaignData.targetRegions.length === 0) {
          toast.error("Please select at least one target region")
          return false
        }
        if (campaignData.targetCategories.length === 0) {
          toast.error("Please select at least one target category")
          return false
        }
        return true
      case 4:
        if (!campaignData.startDate) {
          toast.error("Start date is required")
          return false
        }
        if (campaignData.endDate && campaignData.endDate <= campaignData.startDate) {
          toast.error("End date must be after start date")
          return false
        }
        return true
      default:
        return true
    }
  }

  const handleSubmit = async () => {
    try {
      setIsLoading(true)

      // Prepare campaign data for API
      const campaignPayload: CampaignCreateData = {
        name: campaignData.name,
        description: campaignData.description,
        budget: campaignData.budget,
        dailyBudget: campaignData.dailyBudget,
        pricingModel: campaignData.pricingModel,
        bidAmount: campaignData.bidAmount,
        targetRegions: campaignData.targetRegions,
        targetCategories: campaignData.targetCategories,
        startDate: campaignData.startDate,
        endDate: campaignData.endDate,
      }

      const response = await fetch("/api/advertiser/campaigns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(campaignPayload),
      })

      if (!response.ok) {
        const errorData = await response.json()

        // Handle insufficient balance error specifically
        if (errorData.error === "INSUFFICIENT_BALANCE") {
          const shortfall = errorData.shortfall || 0
          toast.error(
            `Insufficient balance! You need ₹${shortfall.toFixed(2)} more to create this campaign.`,
            {
              action: {
                label: "Add Funds",
                onClick: () => router.push("/dashboard/advertiser/billing"),
              },
            }
          )
          return
        }

        throw new Error(errorData.message || "Failed to create campaign")
      }

      const data = await response.json()

      toast.success("Campaign created successfully!")
      router.push("/dashboard/advertiser/campaigns")
    } catch (error) {
      console.error("Failed to create campaign:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create campaign")
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Campaign Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Summer Sale 2024"
                value={campaignData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your campaign goals and target audience..."
                value={campaignData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Ad Format</Label>
              <RadioGroup
                value={campaignData.adFormat}
                onValueChange={(value) => handleInputChange("adFormat", value as AdFormat)}
                className="grid grid-cols-2 gap-4"
              >
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="BANNER" id="banner" />
                  <Label htmlFor="banner">Banner Ads</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="VIDEO" id="video" />
                  <Label htmlFor="video">Video Ads</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="NATIVE" id="native" />
                  <Label htmlFor="native">Native Ads</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="POPUP" id="popup" />
                  <Label htmlFor="popup">Popup Ads</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="budget">Total Budget *</Label>
                <Input
                  id="budget"
                  type="number"
                  min="1"
                  step="0.01"
                  value={campaignData.budget}
                  onChange={(e) => handleInputChange("budget", parseFloat(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dailyBudget">Daily Budget *</Label>
                <Input
                  id="dailyBudget"
                  type="number"
                  min="1"
                  step="0.01"
                  value={campaignData.dailyBudget}
                  onChange={(e) => handleInputChange("dailyBudget", parseFloat(e.target.value))}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Pricing Model</Label>
              <RadioGroup
                value={campaignData.pricingModel}
                onValueChange={(value) => handleInputChange("pricingModel", value as PricingModel)}
                className="grid grid-cols-3 gap-4"
              >
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="CPC" id="cpc" />
                  <div>
                    <Label htmlFor="cpc">CPC</Label>
                    <p className="text-xs text-muted-foreground">Cost per click</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="CPM" id="cpm" />
                  <div>
                    <Label htmlFor="cpm">CPM</Label>
                    <p className="text-xs text-muted-foreground">Cost per 1000 impressions</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4">
                  <RadioGroupItem value="CPA" id="cpa" />
                  <div>
                    <Label htmlFor="cpa">CPA</Label>
                    <p className="text-xs text-muted-foreground">Cost per acquisition</p>
                  </div>
                </div>
              </RadioGroup>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bidAmount">
                Bid Amount * ({campaignData.pricingModel === "CPM" ? "per 1000 impressions" : `per ${campaignData.pricingModel.toLowerCase().slice(2)}`})
              </Label>
              <Input
                id="bidAmount"
                type="number"
                min="0.01"
                step="0.01"
                value={campaignData.bidAmount}
                onChange={(e) => handleInputChange("bidAmount", parseFloat(e.target.value))}
              />
            </div>

            {/* Frequency Settings */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium">Frequency Management</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="frequencyCap">Frequency Cap *</Label>
                  <Input
                    id="frequencyCap"
                    type="number"
                    min="1"
                    max="50"
                    value={campaignData.frequencyCap}
                    onChange={(e) => handleInputChange("frequencyCap", parseInt(e.target.value) || 1)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum times an ad is shown to the same user
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="frequencyPeriod">Frequency Period *</Label>
                  <Select
                    value={campaignData.frequencyPeriod}
                    onValueChange={(value) => handleInputChange("frequencyPeriod", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAILY">Daily</SelectItem>
                      <SelectItem value="WEEKLY">Weekly</SelectItem>
                      <SelectItem value="CAMPAIGN">Campaign Duration</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Time period for frequency cap
                  </p>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Recommended:</strong> 3-5 impressions per day for brand awareness, 2-3 for performance campaigns
                </AlertDescription>
              </Alert>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Estimated campaign duration: {Math.ceil(campaignData.budget / (campaignData.dailyBudget || 1))} days
              </AlertDescription>
            </Alert>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <Label>Target Regions *</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {regions.map((region) => (
                  <div key={region} className="flex items-center space-x-2">
                    <Checkbox
                      id={region}
                      checked={campaignData.targetRegions.includes(region)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          handleInputChange("targetRegions", [...campaignData.targetRegions, region])
                        } else {
                          handleInputChange("targetRegions", campaignData.targetRegions.filter(r => r !== region))
                        }
                      }}
                    />
                    <Label htmlFor={region} className="text-sm">{region}</Label>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="space-y-4">
              <Label>Target Categories *</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {categories.map((category) => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      id={category}
                      checked={campaignData.targetCategories.includes(category)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          handleInputChange("targetCategories", [...campaignData.targetCategories, category])
                        } else {
                          handleInputChange("targetCategories", campaignData.targetCategories.filter(c => c !== category))
                        }
                      }}
                    />
                    <Label htmlFor={category} className="text-sm">{category}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Start Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !campaignData.startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {campaignData.startDate ? format(campaignData.startDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={campaignData.startDate}
                      onSelect={(date) => date && handleInputChange("startDate", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label>End Date (Optional)</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !campaignData.endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {campaignData.endDate ? format(campaignData.endDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={campaignData.endDate}
                      onSelect={(date) => handleInputChange("endDate", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {campaignData.endDate && campaignData.startDate
                  ? `Campaign will run for ${Math.ceil((campaignData.endDate.getTime() - campaignData.startDate.getTime()) / (1000 * 60 * 60 * 24))} days`
                  : "Campaign will run indefinitely until budget is exhausted or manually stopped"
                }
              </AlertDescription>
            </Alert>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Campaign Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Name</Label>
                      <p className="text-sm text-muted-foreground">{campaignData.name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Ad Format</Label>
                      <p className="text-sm text-muted-foreground">{campaignData.adFormat}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Total Budget</Label>
                      <p className="text-sm text-muted-foreground">{formatCurrency(campaignData.budget)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Daily Budget</Label>
                      <p className="text-sm text-muted-foreground">{formatCurrency(campaignData.dailyBudget || 0)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Pricing Model</Label>
                      <p className="text-sm text-muted-foreground">{campaignData.pricingModel}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Bid Amount</Label>
                      <p className="text-sm text-muted-foreground">{formatCurrency(campaignData.bidAmount)}</p>
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Target Regions</Label>
                    <p className="text-sm text-muted-foreground">{campaignData.targetRegions.join(", ")}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Target Categories</Label>
                    <p className="text-sm text-muted-foreground">{campaignData.targetCategories.join(", ")}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Schedule</Label>
                    <p className="text-sm text-muted-foreground">
                      {campaignData.startDate ? format(campaignData.startDate, "PPP") : "Not set"}
                      {campaignData.endDate && ` - ${format(campaignData.endDate, "PPP")}`}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Create Campaign</h2>
            <p className="text-muted-foreground">
              Set up a new advertising campaign to reach your target audience
            </p>
          </div>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full border-2",
                    currentStep >= step.id 
                      ? "bg-primary border-primary text-primary-foreground" 
                      : "border-muted-foreground text-muted-foreground"
                  )}>
                    <step.icon className="h-4 w-4" />
                  </div>
                  <div className="ml-2">
                    <p className={cn(
                      "text-sm font-medium",
                      currentStep >= step.id ? "text-primary" : "text-muted-foreground"
                    )}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "w-12 h-0.5 mx-4",
                      currentStep > step.id ? "bg-primary" : "bg-muted"
                    )} />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle>{steps[currentStep - 1].title}</CardTitle>
            <CardDescription>
              {currentStep === 1 && "Enter basic information about your campaign"}
              {currentStep === 2 && "Set your budget and pricing preferences"}
              {currentStep === 3 && "Choose your target audience"}
              {currentStep === 4 && "Schedule when your campaign will run"}
              {currentStep === 5 && "Review your campaign details before creating"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          
          {currentStep < steps.length ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Campaign"}
            </Button>
          )}
        </div>
      </div>
    </AdvertiserLayout>
  )
}
