import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { headers } from "next/headers"
import { parseTrackingId, getClientIP, sanitizeUserAgent, sanitizeReferrer } from "@/lib/tracking-utils"
import { FrequencyManager } from "@/lib/frequency/frequency-manager"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trackingId = searchParams.get("id")

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    // Parse tracking ID to extract campaign, ad, and ad space info
    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    // Get client information
    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = sanitizeUserAgent(headersList.get("user-agent") || "")
    const referrer = sanitizeReferrer(headersList.get("referer") || "")

    // Record the impression
    await recordImpression({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
    })

    // Record frequency tracking
    const frequencyManager = FrequencyManager.getInstance()
    await frequencyManager.recordImpression({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      ipAddress,
      userAgent,
      deviceType: getDeviceType(userAgent)
    })

    // Return a 1x1 transparent pixel
    const pixel = Buffer.from(
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      "base64"
    )

    return new NextResponse(pixel, {
      status: 200,
      headers: {
        "Content-Type": "image/png",
        "Content-Length": pixel.length.toString(),
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    })
  } catch (error) {
    console.error("Impression tracking error:", error)
    
    // Still return a pixel even if tracking fails
    const pixel = Buffer.from(
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      "base64"
    )

    return new NextResponse(pixel, {
      status: 200,
      headers: {
        "Content-Type": "image/png",
        "Content-Length": pixel.length.toString(),
      },
    })
  }
}

export async function POST(request: NextRequest) {
  // Support POST requests for impression tracking
  try {
    const body = await request.json()
    const { trackingId, metadata } = body

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = sanitizeUserAgent(headersList.get("user-agent") || "")
    const referrer = sanitizeReferrer(headersList.get("referer") || "")

    await recordImpression({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
      metadata,
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Impression tracking error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}



interface ImpressionData {
  campaignId: string
  adId: string
  adSpaceId: string
  ipAddress: string
  userAgent: string
  referrer: string
  timestamp: Date
  metadata?: any
}

async function recordImpression(data: ImpressionData): Promise<void> {
  try {
    // Skip recording for fallback ads
    if (data.campaignId === "fallback") {
      console.log("Fallback impression recorded")
      return
    }

    // Find or create ad placement record
    let adPlacement = await prisma.adPlacement.findFirst({
      where: {
        campaignId: data.campaignId,
        adId: data.adId,
        adSpaceId: data.adSpaceId,
      },
    })

    if (!adPlacement) {
      // Create new ad placement if it doesn't exist
      adPlacement = await prisma.adPlacement.create({
        data: {
          campaignId: data.campaignId,
          adId: data.adId,
          adSpaceId: data.adSpaceId,
          isActive: true,
          impressions: 1,
          clicks: 0,
          conversions: 0,
          revenue: 0,
          cost: 0,
        },
      })
    } else {
      // Update existing ad placement
      await prisma.adPlacement.update({
        where: { id: adPlacement.id },
        data: {
          impressions: { increment: 1 },
        },
      })
    }

    // Update publisher profile balance (for impressions in CPM campaigns)
    const campaign = await prisma.campaign.findUnique({
      where: { id: data.campaignId },
    })

    if (campaign && campaign.pricingModel === "CPM") {
      const cost = campaign.bidAmount / 1000 // CPM is per 1000 impressions
      const publisherRevenue = cost * 0.7 // 70% revenue share to publisher

      await prisma.adPlacement.update({
        where: { id: adPlacement.id },
        data: {
          revenue: { increment: publisherRevenue },
          cost: { increment: cost },
        },
      })

      // Update publisher balance
      const adSpace = await prisma.adSpace.findUnique({
        where: { id: data.adSpaceId },
        include: { publisher: true },
      })

      if (adSpace) {
        await prisma.publisherProfile.update({
          where: { id: adSpace.publisherId },
          data: {
            balance: { increment: publisherRevenue },
          },
        })
      }

      // Update advertiser spending
      await prisma.advertiserProfile.update({
        where: { id: campaign.advertiserId },
        data: {
          balance: { decrement: cost },
        },
      })

      // Broadcast real-time update
      try {
        const { broadcastUpdate } = await import("@/lib/realtime/broadcast")
        broadcastUpdate({
          type: "impression",
          campaignId: data.campaignId,
          adSpaceId: data.adSpaceId,
          cost,
          revenue: publisherRevenue,
          pricingModel: "CPM"
        })
      } catch (error) {
        console.error("Error broadcasting impression update:", error)
      }
    }

    // Log for analytics
    console.log("Impression recorded:", {
      campaignId: data.campaignId,
      adId: data.adId,
      adSpaceId: data.adSpaceId,
      timestamp: data.timestamp,
    })

    // In a real implementation, you might also:
    // 1. Store detailed impression data in a separate analytics table
    // 2. Send to a real-time analytics service
    // 3. Update campaign daily/hourly metrics
    // 4. Trigger fraud detection algorithms
    // 5. Update bidding algorithm performance data
  } catch (error) {
    console.error("Failed to record impression:", error)
    // Don't throw error to avoid breaking the tracking pixel
  }
}

/**
 * Detect device type from user agent
 */
function getDeviceType(userAgent: string): string {
  const isMobile = /Mobile|Android|iPhone/i.test(userAgent)
  const isTablet = /iPad|Tablet/i.test(userAgent)

  if (isTablet) return "tablet"
  if (isMobile) return "mobile"
  return "desktop"
}
