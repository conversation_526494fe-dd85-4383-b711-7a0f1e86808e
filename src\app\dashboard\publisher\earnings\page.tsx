"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { 
  DollarSign, 
  TrendingUp, 
  Calendar as CalendarIcon, 
  Download, 
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { format, subDays } from "date-fns"

interface EarningsData {
  date: string
  earnings: number
  impressions: number
  clicks: number
}

interface PayoutHistory {
  id: string
  amount: number
  status: "pending" | "processing" | "completed" | "failed"
  requestedAt: Date
  processedAt?: Date
  method: string
}

const chartConfig = {
  earnings: {
    label: "Earnings",
    color: "hsl(var(--chart-1))",
  },
}

export default function EarningsPage() {
  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })
  const [earningsData, setEarningsData] = useState<EarningsData[]>([])
  const [payoutHistory, setPayoutHistory] = useState<PayoutHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const currentBalance = 156.75
  const minimumPayout = 50.00
  const nextPayoutDate = new Date(2024, 2, 15) // March 15, 2024

  useEffect(() => {
    fetchEarningsData()
    fetchPayoutHistory()
  }, [dateRange])

  const fetchEarningsData = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockData: EarningsData[] = []
      const days = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))
      
      for (let i = 0; i < days; i++) {
        const date = new Date(dateRange.from)
        date.setDate(date.getDate() + i)
        
        const impressions = Math.floor(Math.random() * 2000) + 500
        const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01))
        const earnings = clicks * (Math.random() * 0.5 + 0.2)
        
        mockData.push({
          date: format(date, "MMM dd"),
          earnings: parseFloat(earnings.toFixed(2)),
          impressions,
          clicks,
        })
      }
      
      setEarningsData(mockData)
    } catch (error) {
      console.error("Failed to fetch earnings data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchPayoutHistory = async () => {
    try {
      // Mock payout history
      const mockPayouts: PayoutHistory[] = [
        {
          id: "1",
          amount: 125.50,
          status: "completed",
          requestedAt: new Date("2024-02-01"),
          processedAt: new Date("2024-02-03"),
          method: "PayPal",
        },
        {
          id: "2",
          amount: 89.25,
          status: "completed",
          requestedAt: new Date("2024-01-01"),
          processedAt: new Date("2024-01-03"),
          method: "Bank Transfer",
        },
        {
          id: "3",
          amount: 67.80,
          status: "processing",
          requestedAt: new Date("2024-02-28"),
          method: "PayPal",
        },
      ]
      setPayoutHistory(mockPayouts)
    } catch (error) {
      console.error("Failed to fetch payout history:", error)
    }
  }

  const totalEarnings = earningsData.reduce((sum, day) => sum + day.earnings, 0)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>
      case "processing":
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Processing</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>
      case "failed":
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Failed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const canRequestPayout = currentBalance >= minimumPayout

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Earnings</h2>
            <p className="text-muted-foreground">
              Track your revenue and manage payouts
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[280px] justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange?.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange?.from}
                  selected={dateRange}
                  onSelect={(range) => {
                    if (range?.from && range?.to) {
                      setDateRange({ from: range.from, to: range.to })
                    }
                  }}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
            
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Earnings Summary */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(currentBalance)}</div>
              <p className="text-xs text-muted-foreground">
                Available for payout
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Period Earnings</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalEarnings)}</div>
              <p className="text-xs text-muted-foreground">
                Selected date range
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Payout</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{format(nextPayoutDate, "MMM dd")}</div>
              <p className="text-xs text-muted-foreground">
                Automatic payout date
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Minimum Payout</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(minimumPayout)}</div>
              <p className="text-xs text-muted-foreground">
                Required for payout
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Payout Request */}
        <Card>
          <CardHeader>
            <CardTitle>Request Payout</CardTitle>
            <CardDescription>
              Request a payout of your current balance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Available Balance: {formatCurrency(currentBalance)}</p>
                <p className="text-xs text-muted-foreground">
                  {canRequestPayout 
                    ? "You can request a payout now" 
                    : `You need ${formatCurrency(minimumPayout - currentBalance)} more to request a payout`
                  }
                </p>
              </div>
              <Button disabled={!canRequestPayout}>
                <CreditCard className="mr-2 h-4 w-4" />
                Request Payout
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Earnings Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Earnings Trend</CardTitle>
            <CardDescription>
              Your earnings over the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={earningsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area
                    type="monotone"
                    dataKey="earnings"
                    stroke={chartConfig.earnings.color}
                    fill={chartConfig.earnings.color}
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Payout History */}
        <Card>
          <CardHeader>
            <CardTitle>Payout History</CardTitle>
            <CardDescription>
              Your recent payout requests and their status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Requested</TableHead>
                  <TableHead>Processed</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payoutHistory.map((payout) => (
                  <TableRow key={payout.id}>
                    <TableCell className="font-medium">
                      {formatCurrency(payout.amount)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(payout.status)}
                    </TableCell>
                    <TableCell>{payout.method}</TableCell>
                    <TableCell>{format(payout.requestedAt, "MMM dd, yyyy")}</TableCell>
                    <TableCell>
                      {payout.processedAt 
                        ? format(payout.processedAt, "MMM dd, yyyy")
                        : "-"
                      }
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
    </div>
  )
}
