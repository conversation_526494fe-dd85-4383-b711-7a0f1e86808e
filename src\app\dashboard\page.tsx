"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Loader2 } from "lucide-react"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session) {
      router.push("/auth/signin")
      return
    }

    if (!session.user.isOnboarded) {
      // Redirect to onboarding if not completed
      if (session.user.role === "PUBLISHER") {
        router.push("/onboarding/publisher")
      } else if (session.user.role === "ADVERTISER") {
        router.push("/onboarding/advertiser")
      }
      return
    }

    // Redirect to role-specific dashboard
    if (session.user.role === "PUBLISHER") {
      router.push("/dashboard/publisher")
    } else if (session.user.role === "ADVERTISER") {
      router.push("/dashboard/advertiser")
    } else if (session.user.role === "ADMIN") {
      router.push("/dashboard/admin")
    }
  }, [session, status, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        <p className="text-muted-foreground">Redirecting to your dashboard...</p>
      </div>
    </div>
  )
}
