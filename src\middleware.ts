import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const isAuth = !!token
    const isAuthPage = req.nextUrl.pathname.startsWith("/auth")
    const isOnboardingPage = req.nextUrl.pathname.startsWith("/onboarding")
    const isDashboardPage = req.nextUrl.pathname.startsWith("/dashboard")
    const isApiRoute = req.nextUrl.pathname.startsWith("/api")
    const isHomePage = req.nextUrl.pathname === "/"

    // Allow API routes and home page to handle their own logic
    if (isApiRoute || isHomePage) {
      return NextResponse.next()
    }

    // Redirect unauthenticated users to sign in
    if (!isAuth && (isDashboardPage || isOnboardingPage)) {
      return NextResponse.redirect(new URL("/auth/signin", req.url))
    }

    // Handle authenticated users
    if (isAuth) {
      // Redirect authenticated users away from auth pages
      if (isAuthPage) {
        if (!token.isOnboarded) {
          // Redirect to appropriate onboarding based on role
          if (token.role === "PUBLISHER") {
            return NextResponse.redirect(new URL("/onboarding/publisher", req.url))
          } else if (token.role === "ADVERTISER") {
            return NextResponse.redirect(new URL("/onboarding/advertiser", req.url))
          }
        }
        return NextResponse.redirect(new URL("/dashboard", req.url))
      }

      // Handle onboarding flow for non-onboarded users
      if (!token.isOnboarded) {
        if (!isOnboardingPage) {
          // Redirect to appropriate onboarding based on role
          if (token.role === "PUBLISHER") {
            return NextResponse.redirect(new URL("/onboarding/publisher", req.url))
          } else if (token.role === "ADVERTISER") {
            return NextResponse.redirect(new URL("/onboarding/advertiser", req.url))
          }
        }
        // Allow access to correct onboarding page
        if (isOnboardingPage) {
          const correctOnboardingPath = token.role === "PUBLISHER" ? "/onboarding/publisher" : "/onboarding/advertiser"
          if (req.nextUrl.pathname !== correctOnboardingPath) {
            return NextResponse.redirect(new URL(correctOnboardingPath, req.url))
          }
        }
      }

      // Redirect onboarded users away from onboarding pages
      if (token.isOnboarded && isOnboardingPage) {
        return NextResponse.redirect(new URL("/dashboard", req.url))
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: () => true, // Let the middleware function handle the logic
    },
  }
)

export const config = {
  matcher: [
    "/",
    "/dashboard/:path*",
    "/onboarding/:path*",
    "/auth/:path*",
    "/api/protected/:path*",
  ],
}
