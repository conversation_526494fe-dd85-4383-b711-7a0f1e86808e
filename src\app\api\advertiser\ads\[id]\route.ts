import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { AdFormat } from "@prisma/client"

const adUpdateSchema = z.object({
  title: z.string().min(1, "Ad title is required").optional(),
  description: z.string().optional(),
  imageUrl: z.string().url("Invalid image URL").optional(),
  videoUrl: z.string().url("Invalid video URL").optional(),
  clickUrl: z.string().url("Invalid click URL").optional(),
  format: z.enum(["BANNER", "VIDEO", "NATIVE", "POPUP"]).optional(),
  width: z.number().min(1, "Width must be greater than 0").optional(),
  height: z.number().min(1, "Height must be greater than 0").optional(),
  isActive: z.boolean().optional(),
})

// GET /api/advertiser/ads/[id] - Get specific ad details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get ad with full details
    const ad = await prisma.ad.findUnique({
      where: { id },
      include: {
        campaign: {
          include: {
            advertiser: {
              include: {
                user: true,
              },
            },
          },
        },
        adPlacements: {
          include: {
            adSpace: {
              include: {
                publisher: {
                  select: {
                    websiteName: true,
                    websiteUrl: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!ad) {
      return NextResponse.json(
        { message: "Ad not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad
    if (ad.campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Calculate ad metrics
    const totalImpressions = ad.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
    const totalClicks = ad.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
    const totalConversions = ad.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
    const totalCost = ad.adPlacements.reduce((sum, p) => sum + p.cost, 0)
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const costPerClick = totalClicks > 0 ? totalCost / totalClicks : 0

    const adWithMetrics = {
      id: ad.id,
      campaignId: ad.campaignId,
      campaignName: ad.campaign.name,
      title: ad.title,
      description: ad.description,
      imageUrl: ad.imageUrl,
      videoUrl: ad.videoUrl,
      clickUrl: ad.clickUrl,
      format: ad.format,
      width: ad.width,
      height: ad.height,
      isActive: ad.isActive,
      createdAt: ad.createdAt,
      updatedAt: ad.updatedAt,
      // Computed metrics
      impressions: totalImpressions,
      clicks: totalClicks,
      conversions: totalConversions,
      ctr: parseFloat(ctr.toFixed(2)),
      costPerClick: parseFloat(costPerClick.toFixed(2)),
      // Placements info
      placements: ad.adPlacements.map((placement) => ({
        id: placement.id,
        adSpaceName: placement.adSpace.name,
        publisherName: placement.adSpace.publisher.websiteName,
        publisherUrl: placement.adSpace.publisher.websiteUrl,
        impressions: placement.impressions,
        clicks: placement.clicks,
        conversions: placement.conversions,
        cost: placement.cost,
        isActive: placement.isActive,
      })),
    }

    return NextResponse.json({
      ad: adWithMetrics,
    })
  } catch (error) {
    console.error("Ad details error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/advertiser/ads/[id] - Update ad
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = adUpdateSchema.parse(body)

    // Get ad and verify ownership
    const ad = await prisma.ad.findUnique({
      where: { id },
      include: {
        campaign: {
          include: {
            advertiser: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    })

    if (!ad) {
      return NextResponse.json(
        { message: "Ad not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad
    if (ad.campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Validate format-specific requirements
    const newFormat = validatedData.format || ad.format
    if (newFormat === "VIDEO" && !validatedData.videoUrl && !ad.videoUrl) {
      return NextResponse.json(
        { message: "Video URL is required for video ads" },
        { status: 400 }
      )
    }

    if (newFormat !== "VIDEO" && !validatedData.imageUrl && !ad.imageUrl) {
      return NextResponse.json(
        { message: "Image URL is required for non-video ads" },
        { status: 400 }
      )
    }

    // Update ad
    const updatedAd = await prisma.ad.update({
      where: { id },
      data: {
        ...(validatedData.title && { title: validatedData.title }),
        ...(validatedData.description !== undefined && { description: validatedData.description }),
        ...(validatedData.imageUrl && { imageUrl: validatedData.imageUrl }),
        ...(validatedData.videoUrl && { videoUrl: validatedData.videoUrl }),
        ...(validatedData.clickUrl && { clickUrl: validatedData.clickUrl }),
        ...(validatedData.format && { format: validatedData.format as AdFormat }),
        ...(validatedData.width && { width: validatedData.width }),
        ...(validatedData.height && { height: validatedData.height }),
        ...(validatedData.isActive !== undefined && { isActive: validatedData.isActive }),
        updatedAt: new Date(),
      },
      include: {
        campaign: {
          select: {
            name: true,
          },
        },
      },
    })

    return NextResponse.json({
      message: "Ad updated successfully",
      ad: {
        id: updatedAd.id,
        title: updatedAd.title,
        campaignName: updatedAd.campaign.name,
        format: updatedAd.format,
        isActive: updatedAd.isActive,
      },
    })
  } catch (error) {
    console.error("Ad update error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/advertiser/ads/[id] - Delete ad
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get ad and verify ownership
    const ad = await prisma.ad.findUnique({
      where: { id },
      include: {
        campaign: {
          include: {
            advertiser: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    })

    if (!ad) {
      return NextResponse.json(
        { message: "Ad not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad
    if (ad.campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if ad can be deleted (campaign should not be active)
    if (ad.campaign.status === "ACTIVE") {
      return NextResponse.json(
        { message: "Cannot delete ads from active campaigns. Deactivate the ad instead." },
        { status: 400 }
      )
    }

    // Delete ad (this will cascade delete placements)
    await prisma.ad.delete({
      where: { id },
    })

    return NextResponse.json({
      message: "Ad deleted successfully",
    })
  } catch (error) {
    console.error("Ad deletion error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
