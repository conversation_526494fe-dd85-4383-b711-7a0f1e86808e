"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  DollarSign,
  Globe,
  Key,
  Settings,
  Target,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react"

import { NavMain } from "@/components/dashboardLayout/nav-main"
import { NavUser } from "@/components/dashboardLayout/nav-user"
import { TeamSwitcher } from "@/components/dashboardLayout/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

const publisherData = {
  user: {
    name: "Publisher",
    email: "<EMAIL>",
    avatar: "/avatars/publisher.jpg",
  },
  teams: [
    {
      name: "Ad Network",
      logo: Globe,
      plan: "Publisher",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard/publisher",
      icon: BarChart3,
      isActive: true,
    },
    {
      title: "Ad Spaces",
      url: "/dashboard/publisher/ad-spaces",
      icon: Target,
    },
    {
      title: "Analytics",
      url: "/dashboard/publisher/analytics",
      icon: TrendingUp,
    },
    {
      title: "Earnings",
      url: "/dashboard/publisher/earnings",
      icon: DollarSign,
    },
    {
      title: "API Keys",
      url: "/dashboard/publisher/api-keys",
      icon: Key,
    },
    {
      title: "Settings",
      url: "/dashboard/publisher/settings",
      icon: Settings,
    },
  ],
}

const advertiserData = {
  user: {
    name: "Advertiser",
    email: "<EMAIL>",
    avatar: "/avatars/advertiser.jpg",
  },
  teams: [
    {
      name: "Ad Network",
      logo: Zap,
      plan: "Advertiser",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard/advertiser",
      icon: BarChart3,
      isActive: true,
    },
    {
      title: "Campaigns",
      url: "/dashboard/advertiser/campaigns",
      icon: Target,
    },
    {
      title: "Ads",
      url: "/dashboard/advertiser/ads",
      icon: Users,
    },
    {
      title: "Analytics",
      url: "/dashboard/advertiser/analytics",
      icon: TrendingUp,
    },
    {
      title: "Billing",
      url: "/dashboard/advertiser/billing",
      icon: DollarSign,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const isPublisher = pathname.startsWith('/dashboard/publisher')
  const data = isPublisher ? publisherData : advertiserData

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
