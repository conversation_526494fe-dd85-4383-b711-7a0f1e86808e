"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"

import {
  Plus,
  Edit,
  Trash2,
  Eye,
  MousePointer,
  DollarSign,
  Globe,
  Settings,
  Copy,
  Code,
  Scan,
  Wand2,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"
import { AdFormat } from "@prisma/client"
import { useAppDispatch, useAppSelector } from "@/lib/store"
import {
  fetchAdSpaces,
  createAdSpace,
  updateAdSpace,
  scanWebsite,
  clearError,
  clearScanResults
} from "@/store/slices/adSpacesSlice"
import {
  setCreateAdSpaceDialogOpen,
  setScanDialogOpen
} from "@/store/slices/uiSlice"

export default function AdSpacesPage() {
  const dispatch = useAppDispatch()

  // Redux state
  const {
    adSpaces,
    isLoading,
    isScanning,
    isCreating,
    scanResults,
    error
  } = useAppSelector((state) => state.adSpaces)

  const {
    isCreateAdSpaceDialogOpen,
    isScanDialogOpen
  } = useAppSelector((state) => state.ui)

  // Local state for form
  const [newAdSpace, setNewAdSpace] = useState({
    name: "",
    format: "BANNER" as AdFormat,
    width: 728,
    height: 90,
    position: "",
  })

  useEffect(() => {
    dispatch(fetchAdSpaces())
  }, [dispatch])

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast.error(error)
      dispatch(clearError())
    }
  }, [error, dispatch])

  const handleCreateAdSpace = async () => {
    try {
      // Validate form
      if (!newAdSpace.name || !newAdSpace.position) {
        toast.error("Please fill in all required fields")
        return
      }

      const result = await dispatch(createAdSpace({
        ...newAdSpace,
        isActive: true
      }))

      if (createAdSpace.fulfilled.match(result)) {
        dispatch(setCreateAdSpaceDialogOpen(false))
        setNewAdSpace({
          name: "",
          format: "BANNER",
          width: 728,
          height: 90,
          position: "",
        })
        toast.success("Ad space created successfully!")
      }
    } catch (error) {
      console.error("Failed to create ad space:", error)
    }
  }

  const toggleAdSpaceStatus = async (id: string, isActive: boolean) => {
    try {
      const result = await dispatch(updateAdSpace({
        id,
        updates: { isActive }
      }))

      if (updateAdSpace.fulfilled.match(result)) {
        toast.success(`Ad space ${isActive ? "activated" : "deactivated"}`)
      }
    } catch (error) {
      console.error("Failed to update ad space:", error)
    }
  }

  const copyIntegrationCode = async (adSpace: any) => {
    try {
      // First, get the publisher's API key
      const response = await fetch("/api/publisher/api-keys")
      if (!response.ok) {
        throw new Error("Failed to get API key")
      }

      const { credentials } = await response.json()
      const apiKey = credentials?.apiKey || "YOUR_API_KEY"

      const code = `<!-- Ad Network Integration Code -->
<div id="adspace-${adSpace.id}" style="width: ${adSpace.width}px; height: ${adSpace.height}px;"></div>
<script>
  // Load Ad Network SDK
  (function() {
    var script = document.createElement('script');
    script.src = '${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/sdk/adnetwork-sdk.js';
    script.onload = function() {
      AdNetwork.init({
        apiKey: '${apiKey}',
        adSpaceId: '${adSpace.id}',
        container: 'adspace-${adSpace.id}',
        format: '${adSpace.format}',
        width: ${adSpace.width},
        height: ${adSpace.height}
      });
    };
    document.head.appendChild(script);
  })();
</script>`

      await navigator.clipboard.writeText(code)
      toast.success("Integration code copied to clipboard!")
    } catch (error) {
      console.error("Failed to copy integration code:", error)
      toast.error("Failed to copy integration code")
    }
  }

  const handleScanWebsite = async (autoCreate: boolean = false) => {
    try {
      // Get publisher's website URL from profile
      const profileResponse = await fetch("/api/publisher/dashboard")
      if (!profileResponse.ok) {
        throw new Error("Failed to get publisher profile")
      }

      const profileData = await profileResponse.json()
      const websiteUrl = profileData.profile?.websiteUrl

      if (!websiteUrl) {
        throw new Error("No website URL found in your profile")
      }

      const result = await dispatch(scanWebsite({ url: websiteUrl, autoCreate }))

      if (scanWebsite.fulfilled.match(result)) {
        if (autoCreate && result.payload.autoCreated > 0) {
          toast.success(`Successfully created ${result.payload.autoCreated} ad spaces!`)
          dispatch(setScanDialogOpen(false))
        } else {
          toast.success("Website scanned successfully!")
        }
      }
    } catch (error) {
      console.error("Failed to scan website:", error)
    }
  }

  const createAdSpaceFromSuggestion = async (suggestion: any) => {
    try {
      const result = await dispatch(createAdSpace({
        name: suggestion.name,
        format: suggestion.format,
        width: suggestion.width,
        height: suggestion.height,
        position: suggestion.position,
        isActive: true,
      }))

      if (createAdSpace.fulfilled.match(result)) {
        toast.success(`Created ad space: ${suggestion.name}`)
      }
    } catch (error) {
      console.error("Failed to create ad space from suggestion:", error)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const getFormatBadgeColor = (format: AdFormat) => {
    switch (format) {
      case "BANNER":
        return "bg-blue-100 text-blue-800"
      case "VIDEO":
        return "bg-purple-100 text-purple-800"
      case "NATIVE":
        return "bg-green-100 text-green-800"
      case "POPUP":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Ad Spaces</h2>
            <p className="text-muted-foreground">
              Manage your website ad placements and monitor their performance
            </p>
          </div>
          
          <div className="flex gap-2">
            <Dialog open={isScanDialogOpen} onOpenChange={(open) => dispatch(setScanDialogOpen(open))}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Scan className="mr-2 h-4 w-4" />
                  Scan Website
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Scan Website for Ad Spaces</DialogTitle>
                  <DialogDescription>
                    Automatically analyze your website structure and suggest optimal ad placements
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  {!scanResults ? (
                    <div className="text-center py-6">
                      <Wand2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground mb-4">
                        We'll analyze your website structure and suggest the best ad placements
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button
                          onClick={() => handleScanWebsite(false)}
                          disabled={isScanning}
                        >
                          {isScanning ? "Scanning..." : "Scan & Preview"}
                        </Button>
                        <Button
                          onClick={() => handleScanWebsite(true)}
                          disabled={isScanning}
                          variant="outline"
                        >
                          {isScanning ? "Scanning..." : "Scan & Auto-Create"}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <h4 className="font-medium mb-2">Website Analysis</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Title:</span>
                            <p className="font-medium">{scanResults.websiteInfo?.title}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Mobile Responsive:</span>
                            <p className="font-medium">
                              {scanResults.websiteInfo?.mobileResponsive ? "Yes" : "No"}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Suggested Ad Spaces ({scanResults.suggestions?.length || 0})</h4>
                        <div className="space-y-2 max-h-60 overflow-y-auto">
                          {scanResults.suggestions?.map((suggestion: any, index: number) => (
                            <div key={index} className="border rounded-lg p-3">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <h5 className="font-medium">{suggestion.name}</h5>
                                    <Badge variant="outline">{suggestion.format}</Badge>
                                    <div className="flex items-center gap-1">
                                      {suggestion.confidence >= 0.8 ? (
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                      ) : (
                                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                                      )}
                                      <span className="text-xs text-muted-foreground">
                                        {Math.round(suggestion.confidence * 100)}% confidence
                                      </span>
                                    </div>
                                  </div>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {suggestion.width} × {suggestion.height} • {suggestion.reasoning}
                                  </p>
                                </div>
                                <Button
                                  size="sm"
                                  onClick={() => createAdSpaceFromSuggestion(suggestion)}
                                >
                                  Create
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    dispatch(setScanDialogOpen(false))
                    dispatch(clearScanResults())
                  }}>
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isCreateAdSpaceDialogOpen} onOpenChange={(open) => dispatch(setCreateAdSpaceDialogOpen(open))}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Ad Space
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Create New Ad Space</DialogTitle>
                <DialogDescription>
                  Add a new ad placement to your website
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="e.g., Header Banner"
                    value={newAdSpace.name}
                    onChange={(e) => setNewAdSpace({ ...newAdSpace, name: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="format">Format</Label>
                  <Select
                    value={newAdSpace.format}
                    onValueChange={(value) => setNewAdSpace({ ...newAdSpace, format: value as AdFormat })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BANNER">Banner</SelectItem>
                      <SelectItem value="VIDEO">Video</SelectItem>
                      <SelectItem value="NATIVE">Native</SelectItem>
                      <SelectItem value="POPUP">Popup</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="width">Width (px)</Label>
                    <Input
                      id="width"
                      type="number"
                      value={newAdSpace.width}
                      onChange={(e) => setNewAdSpace({ ...newAdSpace, width: parseInt(e.target.value) })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="height">Height (px)</Label>
                    <Input
                      id="height"
                      type="number"
                      value={newAdSpace.height}
                      onChange={(e) => setNewAdSpace({ ...newAdSpace, height: parseInt(e.target.value) })}
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="position">Position</Label>
                  <Input
                    id="position"
                    placeholder="e.g., header, sidebar, footer"
                    value={newAdSpace.position}
                    onChange={(e) => setNewAdSpace({ ...newAdSpace, position: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" onClick={handleCreateAdSpace}>
                  Create Ad Space
                </Button>
              </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Ad Spaces Table */}
        <Card>
          <CardHeader>
            <CardTitle>Your Ad Spaces</CardTitle>
            <CardDescription>
              Manage and monitor all your ad placements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Format</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Impressions</TableHead>
                  <TableHead>Clicks</TableHead>
                  <TableHead>CTR</TableHead>
                  <TableHead>Earnings</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adSpaces.map((adSpace: any) => (
                  <TableRow key={adSpace.id}>
                    <TableCell className="font-medium">{adSpace.name}</TableCell>
                    <TableCell>
                      <Badge className={getFormatBadgeColor(adSpace.format)}>
                        {adSpace.format}
                      </Badge>
                    </TableCell>
                    <TableCell>{adSpace.width} × {adSpace.height}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={adSpace.isActive}
                          onCheckedChange={(checked) => toggleAdSpaceStatus(adSpace.id, checked)}
                        />
                        <span className="text-sm">
                          {adSpace.isActive ? "Active" : "Inactive"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{formatNumber(adSpace.impressions)}</TableCell>
                    <TableCell>{formatNumber(adSpace.clicks)}</TableCell>
                    <TableCell>{adSpace.ctr.toFixed(2)}%</TableCell>
                    <TableCell>{formatCurrency(adSpace.earnings)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyIntegrationCode(adSpace)}
                          title="Copy integration code"
                        >
                          <Code className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" title="Edit">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" title="Delete">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Performance Summary */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Ad Spaces</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adSpaces.length}</div>
              <p className="text-xs text-muted-foreground">
                {adSpaces.filter((space: any) => space.isActive).length} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(adSpaces.reduce((sum: number, space: any) => sum + (space.impressions || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all ad spaces
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(adSpaces.reduce((sum: number, space: any) => sum + (space.earnings || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                From all ad spaces
              </p>
            </CardContent>
          </Card>
        </div>
    </div>
  )
}
