"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

import { 
  DollarSign, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Globe, 
  Users,
  Calendar,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"
import { toast } from "sonner"

interface PublisherStats {
  totalEarnings: number
  todayEarnings: number
  totalImpressions: number
  todayImpressions: number
  totalClicks: number
  todayClicks: number
  ctr: number
  activeAdSpaces: number
  pendingPayment: number
}

interface RecentActivity {
  id: string
  type: "impression" | "click" | "earning"
  amount?: number
  timestamp: Date
  adSpaceName: string
}

export default function PublisherDashboardPage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<PublisherStats>({
    totalEarnings: 0,
    todayEarnings: 0,
    totalImpressions: 0,
    todayImpressions: 0,
    totalClicks: 0,
    todayClicks: 0,
    ctr: 0,
    activeAdSpaces: 0,
    pendingPayment: 0,
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/publisher/dashboard")
      
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)

        // Convert timestamp strings to Date objects
        const activityWithDates = (data.recentActivity || []).map((activity: any) => ({
          ...activity,
          timestamp: new Date(activity.timestamp)
        }))
        setRecentActivity(activityWithDates)
      } else {
        // Handle error case - set default values
        console.error("Failed to fetch publisher dashboard data")
        setStats({
          totalEarnings: 0,
          todayEarnings: 0,
          totalImpressions: 0,
          todayImpressions: 0,
          totalClicks: 0,
          todayClicks: 0,
          ctr: 0,
          activeAdSpaces: 0,
          pendingPayment: 0,
        })
        
        setRecentActivity([])
      }
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "earning":
        return <DollarSign className="h-4 w-4 text-green-500" />
      case "click":
        return <MousePointer className="h-4 w-4 text-blue-500" />
      case "impression":
        return <Eye className="h-4 w-4 text-purple-500" />
      default:
        return <TrendingUp className="h-4 w-4" />
    }
  }

  const getChangeIndicator = (current: number, previous: number) => {
    const change = ((current - previous) / previous) * 100
    const isPositive = change > 0
    
    return (
      <div className={`flex items-center text-sm ${isPositive ? "text-green-600" : "text-red-600"}`}>
        {isPositive ? <ArrowUpRight className="h-4 w-4" /> : <ArrowDownRight className="h-4 w-4" />}
        {Math.abs(change).toFixed(1)}%
      </div>
    )
  }

  if (!session || session.user.role !== "PUBLISHER") {
    return <div>Access denied</div>
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Dashboard Overview</h2>
            <p className="text-muted-foreground">
              Track your website monetization performance
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString("en-US", { 
                weekday: "long", 
                year: "numeric", 
                month: "long", 
                day: "numeric" 
              })}
            </span>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalEarnings)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatCurrency(stats.todayEarnings)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(stats.totalImpressions)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatNumber(stats.todayImpressions)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clicks</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(stats.totalClicks)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatNumber(stats.todayClicks)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CTR</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.ctr}%</div>
              <p className="text-xs text-muted-foreground">
                Click-through rate
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Status */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Ad Spaces
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">{stats.activeAdSpaces}</div>
              <p className="text-sm text-muted-foreground mb-4">Active ad spaces</p>
              <Button className="w-full" size="sm">
                Manage Ad Spaces
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pending Payment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">{formatCurrency(stats.pendingPayment)}</div>
              <p className="text-sm text-muted-foreground mb-4">Ready for payout</p>
              <Button variant="outline" className="w-full" size="sm">
                Request Payout
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Revenue Goal</span>
                  <span>78%</span>
                </div>
                <Progress value={78} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  ${formatCurrency(stats.totalEarnings)} of $1,600 monthly goal
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest impressions, clicks, and earnings from your ad spaces
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getActivityIcon(activity.type)}
                      <div>
                        <p className="text-sm font-medium">
                          {activity.type === "earning" && `Earned ${formatCurrency(activity.amount!)}`}
                          {activity.type === "click" && "New click"}
                          {activity.type === "impression" && "New impression"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {activity.adSpaceName} • {activity.timestamp instanceof Date && !isNaN(activity.timestamp.getTime())
                            ? activity.timestamp.toLocaleTimeString()
                            : 'Invalid time'}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {activity.type}
                    </Badge>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent activity
                </p>
              )}
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
