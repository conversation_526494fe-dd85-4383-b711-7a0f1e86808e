import { NextRequest } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { addConnection, removeConnection } from "@/lib/realtime/broadcast"

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return new Response("Unauthorized", { status: 401 })
  }

  const { searchParams } = new URL(request.url)
  const role = searchParams.get("role") || session.user.role

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      const connectionId = `${session.user.id}_${Date.now()}`
      addConnection(connectionId, controller)

      // Send initial connection message
      controller.enqueue(`data: ${JSON.stringify({
        type: "connected",
        timestamp: new Date().toISOString(),
        message: "Real-time dashboard connected"
      })}\n\n`)

      // Send initial data
      sendInitialData(controller, session.user.id, role || "PUBLISHER")

      // Set up periodic updates
      const interval = setInterval(async () => {
        try {
          await sendPeriodicUpdate(controller, session.user.id, role || "PUBLISHER")
        } catch (error) {
          console.error("Error sending periodic update:", error)
        }
      }, 5000) // Update every 5 seconds

      // Clean up on close
      request.signal.addEventListener("abort", () => {
        clearInterval(interval)
        removeConnection(connectionId)
        try {
          controller.close()
        } catch (error) {
          // Controller might already be closed
        }
      })
    }
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control"
    }
  })
}

async function sendInitialData(
  controller: ReadableStreamDefaultController,
  userId: string,
  role: string
) {
  try {
    if (role === "PUBLISHER") {
      const publisherData = await getPublisherDashboardData(userId)
      controller.enqueue(`data: ${JSON.stringify({
        type: "initial_data",
        role: "publisher",
        data: publisherData,
        timestamp: new Date().toISOString()
      })}\n\n`)
    } else if (role === "ADVERTISER") {
      const advertiserData = await getAdvertiserDashboardData(userId)
      controller.enqueue(`data: ${JSON.stringify({
        type: "initial_data",
        role: "advertiser",
        data: advertiserData,
        timestamp: new Date().toISOString()
      })}\n\n`)
    }
  } catch (error) {
    console.error("Error sending initial data:", error)
  }
}

async function sendPeriodicUpdate(
  controller: ReadableStreamDefaultController,
  userId: string,
  role: string
) {
  try {
    if (role === "PUBLISHER") {
      const stats = await getPublisherStats(userId)
      const recentActivity = await getPublisherRecentActivity(userId)
      
      controller.enqueue(`data: ${JSON.stringify({
        type: "stats_update",
        role: "publisher",
        data: { stats, recentActivity },
        timestamp: new Date().toISOString()
      })}\n\n`)
    } else if (role === "ADVERTISER") {
      const stats = await getAdvertiserStats(userId)
      const campaignUpdates = await getCampaignUpdates(userId)
      
      controller.enqueue(`data: ${JSON.stringify({
        type: "stats_update",
        role: "advertiser",
        data: { stats, campaignUpdates },
        timestamp: new Date().toISOString()
      })}\n\n`)
    }
  } catch (error) {
    console.error("Error sending periodic update:", error)
  }
}

async function getPublisherDashboardData(userId: string) {
  const publisherProfile = await prisma.publisherProfile.findUnique({
    where: { userId },
    include: {
      adSpaces: {
        include: {
          adPlacements: {
            where: {
              isActive: true
            },
            select: {
              impressions: true,
              clicks: true,
              conversions: true,
              revenue: true,
              createdAt: true
            }
          }
        }
      }
    }
  })

  if (!publisherProfile) return null

  const stats = calculatePublisherStats(publisherProfile)
  const recentActivity = getPublisherRecentActivity(userId)

  return {
    profile: {
      websiteName: publisherProfile.websiteName,
      websiteUrl: publisherProfile.websiteUrl,
      balance: publisherProfile.balance
    },
    stats,
    recentActivity
  }
}

async function getAdvertiserDashboardData(userId: string) {
  const advertiserProfile = await prisma.advertiserProfile.findUnique({
    where: { userId },
    include: {
      campaigns: {
        include: {
          ads: true,
          adPlacements: {
            select: {
              impressions: true,
              clicks: true,
              conversions: true,
              cost: true,
              createdAt: true
            }
          }
        }
      }
    }
  })

  if (!advertiserProfile) return null

  const stats = calculateAdvertiserStats(advertiserProfile)
  const campaignSummary = advertiserProfile.campaigns.map(campaign => ({
    id: campaign.id,
    name: campaign.name,
    status: campaign.status,
    budget: campaign.budget,
    spent: campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0),
    impressions: campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0),
    clicks: campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
  }))

  return {
    profile: {
      companyName: advertiserProfile.companyName,
      balance: advertiserProfile.balance
    },
    stats,
    campaigns: campaignSummary
  }
}

async function getPublisherStats(userId: string) {
  const publisherProfile = await prisma.publisherProfile.findUnique({
    where: { userId },
    include: {
      adSpaces: {
        include: {
          adPlacements: {
            select: {
              impressions: true,
              clicks: true,
              conversions: true,
              revenue: true,
              createdAt: true
            }
          }
        }
      }
    }
  })

  if (!publisherProfile) return null

  return calculatePublisherStats(publisherProfile)
}

async function getAdvertiserStats(userId: string) {
  const advertiserProfile = await prisma.advertiserProfile.findUnique({
    where: { userId },
    include: {
      campaigns: {
        include: {
          adPlacements: {
            select: {
              impressions: true,
              clicks: true,
              conversions: true,
              cost: true,
              createdAt: true
            }
          }
        }
      }
    }
  })

  if (!advertiserProfile) return null

  return calculateAdvertiserStats(advertiserProfile)
}

function calculatePublisherStats(publisherProfile: any) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  let totalImpressions = 0
  let totalClicks = 0
  let totalConversions = 0
  let totalRevenue = 0
  let todayImpressions = 0
  let todayClicks = 0
  let todayRevenue = 0

  publisherProfile.adSpaces.forEach((adSpace: any) => {
    adSpace.adPlacements.forEach((placement: any) => {
      totalImpressions += placement.impressions
      totalClicks += placement.clicks
      totalConversions += placement.conversions
      totalRevenue += placement.revenue

      if (new Date(placement.createdAt) >= today) {
        todayImpressions += placement.impressions
        todayClicks += placement.clicks
        todayRevenue += placement.revenue
      }
    })
  })

  const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

  return {
    totalEarnings: totalRevenue,
    todayEarnings: todayRevenue,
    totalImpressions,
    todayImpressions,
    totalClicks,
    todayClicks,
    ctr: Number(ctr.toFixed(2)),
    activeAdSpaces: publisherProfile.adSpaces.filter((as: any) => as.isActive).length,
    pendingPayment: publisherProfile.balance
  }
}

function calculateAdvertiserStats(advertiserProfile: any) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  let totalSpent = 0
  let totalImpressions = 0
  let totalClicks = 0
  let totalConversions = 0
  let todaySpent = 0
  let todayImpressions = 0
  let todayClicks = 0

  advertiserProfile.campaigns.forEach((campaign: any) => {
    campaign.adPlacements.forEach((placement: any) => {
      totalSpent += placement.cost
      totalImpressions += placement.impressions
      totalClicks += placement.clicks
      totalConversions += placement.conversions

      if (new Date(placement.createdAt) >= today) {
        todaySpent += placement.cost
        todayImpressions += placement.impressions
        todayClicks += placement.clicks
      }
    })
  })

  const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
  const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0

  return {
    totalSpent,
    todaySpent,
    totalImpressions,
    todayImpressions,
    totalClicks,
    todayClicks,
    totalConversions,
    ctr: Number(ctr.toFixed(2)),
    conversionRate: Number(conversionRate.toFixed(2)),
    activeCampaigns: advertiserProfile.campaigns.filter((c: any) => c.status === "ACTIVE").length,
    remainingBalance: advertiserProfile.balance
  }
}

async function getPublisherRecentActivity(userId: string) {
  // Get recent ad placements for activity feed
  const recentPlacements = await prisma.adPlacement.findMany({
    where: {
      adSpace: {
        publisher: {
          userId
        }
      },
      createdAt: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
      }
    },
    include: {
      adSpace: {
        select: { name: true }
      }
    },
    orderBy: { createdAt: "desc" },
    take: 10
  })

  return recentPlacements.map(placement => ({
    id: placement.id,
    type: placement.clicks > 0 ? "click" : "impression",
    amount: placement.revenue,
    timestamp: placement.createdAt,
    adSpaceName: placement.adSpace.name
  }))
}

async function getCampaignUpdates(userId: string) {
  // Get recent campaign performance updates
  const campaigns = await prisma.campaign.findMany({
    where: {
      advertiser: {
        userId
      },
      status: "ACTIVE"
    },
    include: {
      adPlacements: {
        where: {
          createdAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
          }
        }
      }
    },
    take: 5
  })

  return campaigns.map(campaign => ({
    id: campaign.id,
    name: campaign.name,
    recentImpressions: campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0),
    recentClicks: campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0),
    recentCost: campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
  }))
}


