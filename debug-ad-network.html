<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad Network Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
        }
        .debug-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .ad-container {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            min-height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9f9f9;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Ad Network Debug Tool</h1>
        <p>This tool helps you debug your ad network integration and find the correct API keys and ad space IDs.</p>
    </div>

    <!-- Step 1: Get Publisher Info -->
    <div class="container debug-section">
        <h2>Step 1: Get Publisher Information</h2>
        <button onclick="getPublisherInfo()">Get All Publishers & Ad Spaces</button>
        <div id="publisher-info-result" class="result" style="display: none;"></div>
    </div>

    <!-- Step 2: Test API Key -->
    <div class="container debug-section">
        <h2>Step 2: Test API Key</h2>
        <div class="form-group">
            <label for="api-key">API Key:</label>
            <input type="text" id="api-key" placeholder="ak_live_1234567890abcdef1234567890abcdef12345678">
        </div>
        <div class="form-group">
            <label for="ad-space-id">Ad Space ID (optional):</label>
            <input type="text" id="ad-space-id" placeholder="Leave empty to see all ad spaces">
        </div>
        <button onclick="testApiKey()">Test API Key</button>
        <div id="api-test-result" class="result" style="display: none;"></div>
    </div>

    <!-- Step 3: Test Ad Request -->
    <div class="container debug-section">
        <h2>Step 3: Test Ad Request</h2>
        <div class="form-group">
            <label for="test-api-key">API Key:</label>
            <input type="text" id="test-api-key" placeholder="ak_live_1234567890abcdef1234567890abcdef12345678">
        </div>
        <div class="form-group">
            <label for="test-ad-space-id">Ad Space ID:</label>
            <input type="text" id="test-ad-space-id" placeholder="Required for ad request">
        </div>
        <div class="form-group">
            <label for="test-format">Format:</label>
            <select id="test-format">
                <option value="BANNER">BANNER</option>
                <option value="VIDEO">VIDEO</option>
                <option value="NATIVE">NATIVE</option>
                <option value="POPUP">POPUP</option>
            </select>
        </div>
        <div class="form-group">
            <label for="test-width">Width:</label>
            <input type="number" id="test-width" value="728">
        </div>
        <div class="form-group">
            <label for="test-height">Height:</label>
            <input type="number" id="test-height" value="90">
        </div>
        <button onclick="testAdRequest()">Test Ad Request</button>
        <div id="ad-request-result" class="result" style="display: none;"></div>
    </div>

    <!-- Step 4: Live SDK Test -->
    <div class="container debug-section">
        <h2>Step 4: Live SDK Test</h2>
        <div class="form-group">
            <label for="sdk-api-key">API Key:</label>
            <input type="text" id="sdk-api-key" placeholder="ak_live_1234567890abcdef1234567890abcdef12345678">
        </div>
        <div class="form-group">
            <label for="sdk-ad-space-id">Ad Space ID:</label>
            <input type="text" id="sdk-ad-space-id" placeholder="Required for SDK test">
        </div>
        <button onclick="testSDK()">Load Ad with SDK</button>
        <div id="test-ad-container" class="ad-container">
            <span>Ad will appear here</span>
        </div>
        <div id="sdk-test-result" class="result" style="display: none;"></div>
    </div>

    <!-- Step 5: React Component Code -->
    <div class="container debug-section">
        <h2>Step 5: Correct React Component Code</h2>
        <p>Once you have your API key and ad space ID, use this corrected React component:</p>
        <div class="code-block" id="react-code">
            Click "Generate React Code" after testing your API key and ad space ID above.
        </div>
        <button onclick="generateReactCode()">Generate React Code</button>
    </div>

    <!-- Load SDK -->
    <script src="https://ad-network-new-web-app.vercel.app/sdk.js"></script>
    
    <script>
        const API_BASE = 'https://ad-network-new-web-app.vercel.app/api';

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function getPublisherInfo() {
            try {
                const response = await fetch(`${API_BASE}/debug/publisher-info`);
                const data = await response.json();
                
                if (data.success) {
                    let result = `Found ${data.data.totalPublishers} publishers and ${data.data.totalCampaigns} campaigns\n\n`;
                    
                    result += "PUBLISHERS:\n";
                    data.data.publishers.forEach(pub => {
                        result += `\nPublisher: ${pub.websiteName}\n`;
                        result += `API Key: ${pub.apiKey}\n`;
                        result += `Website: ${pub.websiteUrl}\n`;
                        result += `Ad Spaces:\n`;
                        pub.adSpaces.forEach(space => {
                            result += `  - ${space.name} (${space.id})\n`;
                            result += `    Format: ${space.format}, Size: ${space.width}x${space.height}\n`;
                            result += `    Active: ${space.isActive}\n`;
                        });
                    });
                    
                    result += "\n\nCAMPAIGNS:\n";
                    data.data.campaigns.forEach(campaign => {
                        result += `\nCampaign: ${campaign.name}\n`;
                        result += `Status: ${campaign.status}\n`;
                        result += `Budget: $${campaign.budget}\n`;
                        result += `Category: ${campaign.targetCategory}\n`;
                        result += `Region: ${campaign.targetRegion}\n`;
                        result += `Ads: ${campaign.ads.length}\n`;
                    });
                    
                    showResult('publisher-info-result', result, 'success');
                } else {
                    showResult('publisher-info-result', `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('publisher-info-result', `Network Error: ${error.message}`, 'error');
            }
        }

        async function testApiKey() {
            const apiKey = document.getElementById('api-key').value.trim();
            const adSpaceId = document.getElementById('ad-space-id').value.trim();
            
            if (!apiKey) {
                showResult('api-test-result', 'Please enter an API key', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/debug/publisher-info`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ apiKey, adSpaceId: adSpaceId || undefined })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let result = `✅ API Key is valid!\n\n`;
                    result += `Publisher: ${data.publisher.websiteName}\n`;
                    result += `Email: ${data.publisher.email}\n`;
                    result += `Active: ${data.publisher.isActive}\n\n`;
                    
                    if (data.adSpace) {
                        result += `✅ Ad Space Found:\n`;
                        result += `Name: ${data.adSpace.name}\n`;
                        result += `ID: ${data.adSpace.id}\n`;
                        result += `Format: ${data.adSpace.format}\n`;
                        result += `Size: ${data.adSpace.dimensions}\n`;
                        result += `Active: ${data.adSpace.isActive}\n`;
                    } else {
                        result += `Available Ad Spaces:\n`;
                        data.availableAdSpaces.forEach(space => {
                            result += `- ${space.name} (${space.id}) - ${space.format} ${space.dimensions}\n`;
                        });
                    }
                    
                    showResult('api-test-result', result, 'success');
                } else {
                    showResult('api-test-result', `❌ ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('api-test-result', `Network Error: ${error.message}`, 'error');
            }
        }

        async function testAdRequest() {
            const apiKey = document.getElementById('test-api-key').value.trim();
            const adSpaceId = document.getElementById('test-ad-space-id').value.trim();
            const format = document.getElementById('test-format').value;
            const width = parseInt(document.getElementById('test-width').value);
            const height = parseInt(document.getElementById('test-height').value);
            
            if (!apiKey || !adSpaceId) {
                showResult('ad-request-result', 'Please enter both API key and ad space ID', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/ads/request`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        apiKey,
                        adSpaceId,
                        format,
                        width,
                        height,
                        position: 'test'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.ad) {
                    let result = `✅ Ad Request Successful!\n\n`;
                    result += `Ad Title: ${data.ad.title}\n`;
                    result += `Description: ${data.ad.description || 'N/A'}\n`;
                    result += `Click URL: ${data.ad.clickUrl}\n`;
                    result += `Image URL: ${data.ad.imageUrl || 'N/A'}\n`;
                    result += `Bid Amount: $${data.bidAmount}\n`;
                    result += `Pricing Model: ${data.pricingModel}\n`;
                    
                    showResult('ad-request-result', result, 'success');
                } else {
                    let result = `❌ No ads available\n\n`;
                    result += `Error: ${data.error || 'Unknown error'}\n`;
                    if (data.frequencyBlocked) {
                        result += `Reason: Frequency cap exceeded\n`;
                        result += `Reset time: ${data.timeUntilReset || 'Unknown'}\n`;
                    }
                    
                    showResult('ad-request-result', result, 'error');
                }
            } catch (error) {
                showResult('ad-request-result', `Network Error: ${error.message}`, 'error');
            }
        }

        async function testSDK() {
            const apiKey = document.getElementById('sdk-api-key').value.trim();
            const adSpaceId = document.getElementById('sdk-ad-space-id').value.trim();
            
            if (!apiKey || !adSpaceId) {
                showResult('sdk-test-result', 'Please enter both API key and ad space ID', 'error');
                return;
            }

            try {
                // Clear previous ad
                const container = document.getElementById('test-ad-container');
                container.innerHTML = '<span>Loading ad...</span>';
                
                // Initialize SDK
                const initResult = window.AdNetwork.init({
                    apiKey: apiKey,
                    debug: true
                });
                
                if (!initResult) {
                    showResult('sdk-test-result', '❌ SDK initialization failed', 'error');
                    return;
                }
                
                // Load ad
                const loadResult = await window.AdNetwork.loadAd('test-ad-container', {
                    adSpaceId: adSpaceId,
                    format: 'BANNER',
                    width: 728,
                    height: 90
                });
                
                if (loadResult) {
                    showResult('sdk-test-result', '✅ Ad loaded successfully with SDK!', 'success');
                } else {
                    showResult('sdk-test-result', '❌ Failed to load ad with SDK', 'error');
                }
                
            } catch (error) {
                showResult('sdk-test-result', `SDK Error: ${error.message}`, 'error');
            }
        }

        function generateReactCode() {
            const apiKey = document.getElementById('sdk-api-key').value.trim() || 'YOUR_API_KEY';
            const adSpaceId = document.getElementById('sdk-ad-space-id').value.trim() || 'YOUR_AD_SPACE_ID';
            
            const code = `import { useEffect } from 'react';

function AdComponent() {
  useEffect(() => {
    // Load the Ad Network SDK
    const script = document.createElement('script');
    script.src = 'https://ad-network-new-web-app.vercel.app/sdk.js';
    script.onload = () => {
      // Initialize SDK
      window.AdNetwork.init({
        apiKey: '${apiKey}',
        debug: true, // Remove in production
      });
      
      // Load ad with correct parameters
      window.AdNetwork.loadAd('react-ad-container', {
        adSpaceId: '${adSpaceId}', // This was missing!
        format: 'BANNER',
        width: 728,
        height: 90,
        position: 'header'
      });
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  return (
    <div 
      id="react-ad-container" 
      style={{ 
        width: '728px', 
        height: '90px',
        border: '1px solid #ddd',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9'
      }}
    >
      Loading ad...
    </div>
  );
}

export default AdComponent;`;
            
            document.getElementById('react-code').textContent = code;
        }
    </script>
</body>
</html>
