import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { headers } from "next/headers"
import { parseTrackingId, getClientIP, sanitizeUserAgent, sanitizeReferrer } from "@/lib/tracking-utils"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trackingId = searchParams.get("id")
    const conversionValue = searchParams.get("value")
    const conversionType = searchParams.get("type") || "purchase"

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    // Parse tracking ID to extract campaign, ad, and ad space info
    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    // Get client information
    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = sanitizeUserAgent(headersList.get("user-agent") || "")
    const referrer = sanitizeReferrer(headersList.get("referer") || "")

    // Record the conversion
    await recordConversion({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      conversionType,
      conversionValue: conversionValue ? parseFloat(conversionValue) : undefined,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
    })

    // Return a 1x1 transparent pixel for conversion tracking
    const pixel = Buffer.from(
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      "base64"
    )

    return new NextResponse(pixel, {
      status: 200,
      headers: {
        "Content-Type": "image/png",
        "Content-Length": pixel.length.toString(),
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    })
  } catch (error) {
    console.error("Conversion tracking error:", error)
    
    // Still return a pixel even if tracking fails
    const pixel = Buffer.from(
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      "base64"
    )

    return new NextResponse(pixel, {
      status: 200,
      headers: {
        "Content-Type": "image/png",
        "Content-Length": pixel.length.toString(),
      },
    })
  }
}

export async function POST(request: NextRequest) {
  // Support POST requests for conversion tracking
  try {
    const body = await request.json()
    const { trackingId, conversionType, conversionValue, metadata } = body

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    const headersList = await headers()
    const ipAddress = headersList.get("x-forwarded-for") ||
                     headersList.get("x-real-ip") ||
                     "unknown"
    const userAgent = headersList.get("user-agent") || ""
    const referrer = headersList.get("referer") || ""

    await recordConversion({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      conversionType: conversionType || "purchase",
      conversionValue,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
      metadata,
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Conversion tracking error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}



interface ConversionData {
  campaignId: string
  adId: string
  adSpaceId: string
  conversionType: string
  conversionValue?: number
  ipAddress: string
  userAgent: string
  referrer: string
  timestamp: Date
  metadata?: any
}

async function recordConversion(data: ConversionData): Promise<void> {
  try {
    // Skip recording for fallback ads
    if (data.campaignId === "fallback") {
      console.log("Fallback conversion recorded")
      return
    }

    // Find the ad placement record
    let adPlacement = await prisma.adPlacement.findFirst({
      where: {
        campaignId: data.campaignId,
        adId: data.adId,
        adSpaceId: data.adSpaceId,
      },
    })

    if (!adPlacement) {
      console.error("Ad placement not found for conversion tracking")
      return
    }

    // Update conversion count
    await prisma.adPlacement.update({
      where: { id: adPlacement.id },
      data: {
        conversions: { increment: 1 },
      },
    })

    // Handle CPA (Cost Per Acquisition) campaigns
    const campaign = await prisma.campaign.findUnique({
      where: { id: data.campaignId },
    })

    if (campaign && campaign.pricingModel === "CPA") {
      const cost = campaign.bidAmount
      const publisherRevenue = cost * 0.7 // 70% revenue share to publisher

      await prisma.adPlacement.update({
        where: { id: adPlacement.id },
        data: {
          revenue: { increment: publisherRevenue },
          cost: { increment: cost },
        },
      })

      // Update publisher balance
      const adSpace = await prisma.adSpace.findUnique({
        where: { id: data.adSpaceId },
        include: { publisher: true },
      })

      if (adSpace) {
        await prisma.publisherProfile.update({
          where: { id: adSpace.publisherId },
          data: {
            balance: { increment: publisherRevenue },
          },
        })
      }

      // Update advertiser spending
      await prisma.advertiserProfile.update({
        where: { id: campaign.advertiserId },
        data: {
          balance: { decrement: cost },
        },
      })

      // Broadcast real-time update
      try {
        const { broadcastUpdate } = await import("@/lib/realtime/broadcast")
        broadcastUpdate({
          type: "conversion",
          campaignId: data.campaignId,
          adSpaceId: data.adSpaceId,
          cost,
          revenue: publisherRevenue,
          pricingModel: "CPA",
          conversionValue: data.conversionValue
        })
      } catch (error) {
        console.error("Error broadcasting conversion update:", error)
      }
    }

    // Log for analytics
    console.log("Conversion recorded:", {
      campaignId: data.campaignId,
      adId: data.adId,
      adSpaceId: data.adSpaceId,
      conversionType: data.conversionType,
      conversionValue: data.conversionValue,
      timestamp: data.timestamp,
    })

    // In a real implementation, you might also:
    // 1. Store detailed conversion data in a separate analytics table
    // 2. Send to a real-time analytics service
    // 3. Update campaign ROI metrics
    // 4. Trigger automated bid optimization
    // 5. Send conversion notifications to advertisers
    // 6. Update machine learning models for better targeting
    // 7. Calculate lifetime value and attribution
  } catch (error) {
    console.error("Failed to record conversion:", error)
    // Don't throw error to avoid breaking the tracking pixel
  }
}
