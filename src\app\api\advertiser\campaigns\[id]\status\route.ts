import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { CampaignStatus } from "@prisma/client"

const statusUpdateSchema = z.object({
  status: z.enum(["DRAFT", "ACTIVE", "PAUSED", "COMPLETED", "CANCELLED"]),
})

// PUT /api/advertiser/campaigns/[id]/status - Update campaign status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = statusUpdateSchema.parse(body)

    // Get campaign and verify ownership
    const campaign = await prisma.campaign.findUnique({
      where: { id },
      include: {
        advertiser: {
          include: {
            user: true,
          },
        },
        ads: {
          where: {
            isActive: true,
          },
        },
      },
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Validate status transition
    const currentStatus = campaign.status
    const newStatus = validatedData.status

    // Check if status change is allowed
    if (currentStatus === newStatus) {
      return NextResponse.json(
        { message: "Campaign is already in this status" },
        { status: 400 }
      )
    }

    // Business rules for status changes
    if (newStatus === "ACTIVE") {
      // Check if campaign has active ads
      if (campaign.ads.length === 0) {
        return NextResponse.json(
          { 
            message: "Cannot activate campaign without active ads",
            error: "NO_ACTIVE_ADS"
          },
          { status: 400 }
        )
      }

      // Check if advertiser has sufficient balance
      if (campaign.advertiser.balance <= 0) {
        return NextResponse.json(
          { 
            message: "Insufficient balance to activate campaign",
            error: "INSUFFICIENT_BALANCE"
          },
          { status: 400 }
        )
      }

      // Check if start date is in the future
      if (campaign.startDate && campaign.startDate > new Date()) {
        return NextResponse.json(
          { 
            message: "Cannot activate campaign before start date",
            error: "START_DATE_NOT_REACHED"
          },
          { status: 400 }
        )
      }

      // Check if end date has passed
      if (campaign.endDate && campaign.endDate < new Date()) {
        return NextResponse.json(
          { 
            message: "Cannot activate campaign after end date",
            error: "END_DATE_PASSED"
          },
          { status: 400 }
        )
      }
    }

    // Prevent reactivating completed or cancelled campaigns
    if ((currentStatus === "COMPLETED" || currentStatus === "CANCELLED") && newStatus === "ACTIVE") {
      return NextResponse.json(
        { message: "Cannot reactivate completed or cancelled campaigns" },
        { status: 400 }
      )
    }

    // Update campaign status
    const updatedCampaign = await prisma.campaign.update({
      where: { id },
      data: {
        status: newStatus as CampaignStatus,
        updatedAt: new Date(),
      },
    })

    // Log status change in wallet transactions for audit trail
    if (newStatus === "ACTIVE" && currentStatus !== "ACTIVE") {
      await prisma.walletTransaction.create({
        data: {
          advertiserId: campaign.advertiser.id,
          type: "DEBIT",
          amount: 0, // No immediate charge, just logging the activation
          description: `Campaign "${campaign.name}" activated`,
          balanceAfter: campaign.advertiser.balance,
          relatedCampaignId: campaign.id,
        },
      })
    }

    return NextResponse.json({
      success: true,
      message: `Campaign ${newStatus.toLowerCase()} successfully`,
      status: updatedCampaign.status,
      campaign: {
        id: updatedCampaign.id,
        name: updatedCampaign.name,
        status: updatedCampaign.status,
        updatedAt: updatedCampaign.updatedAt,
      },
    })
  } catch (error) {
    console.error("Campaign status update error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid status value", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
