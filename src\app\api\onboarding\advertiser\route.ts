import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const advertiserOnboardingSchema = z.object({
  companyName: z.string().min(2, "Company name must be at least 2 characters"),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
  description: z.string().optional(),
  industry: z.string().min(1, "Industry is required"),
  monthlyBudget: z.string().min(1, "Monthly budget is required"),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = advertiserOnboardingSchema.parse(body)

    // Check if user already has an advertiser profile
    const existingProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (existingProfile) {
      return NextResponse.json(
        { message: "Advertiser profile already exists" },
        { status: 400 }
      )
    }

    // Create advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.create({
      data: {
        userId: session.user.id,
        companyName: validatedData.companyName,
        website: validatedData.website || null,
        description: validatedData.description,
        industry: validatedData.industry,
        budget: parseFloat(validatedData.monthlyBudget),
      },
    })

    // Update user onboarding status
    await prisma.user.update({
      where: { id: session.user.id },
      data: { isOnboarded: true },
    })

    return NextResponse.json(
      {
        message: "Advertiser profile created successfully",
        profile: {
          id: advertiserProfile.id,
          companyName: advertiserProfile.companyName,
          website: advertiserProfile.website,
          industry: advertiserProfile.industry,
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Advertiser onboarding error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
